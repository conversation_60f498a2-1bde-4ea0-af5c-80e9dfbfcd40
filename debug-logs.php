<?php
/**
 * Simple Debug Log Viewer
 * Access: http://your-site.com/debug-logs.php
 */

// Security check
if (!isset($_GET['view']) || $_GET['view'] !== 'logs') {
    die('Access denied');
}

$log_file = __DIR__ . '/wp-content/debug.log';

?>
<!DOCTYPE html>
<html>
<head>
    <title>WordPress Debug Logs</title>
    <style>
        body { font-family: monospace; margin: 20px; }
        .log-container { 
            background: #f5f5f5; 
            padding: 20px; 
            border: 1px solid #ddd; 
            max-height: 600px; 
            overflow-y: auto; 
        }
        .aui-log { color: #0073aa; font-weight: bold; }
        .error { color: #dc3232; }
        .success { color: #46b450; }
        .refresh-btn { 
            background: #0073aa; 
            color: white; 
            padding: 10px 20px; 
            border: none; 
            cursor: pointer; 
            margin-bottom: 20px;
        }
        .clear-btn { 
            background: #dc3232; 
            color: white; 
            padding: 10px 20px; 
            border: none; 
            cursor: pointer; 
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <h1>WordPress Debug Logs</h1>
    
    <button class="refresh-btn" onclick="location.reload()">🔄 Refresh</button>
    <button class="clear-btn" onclick="clearLogs()">🗑️ Clear Logs</button>
    
    <div class="log-container">
        <?php
        if (file_exists($log_file)) {
            $logs = file_get_contents($log_file);
            
            // Get only recent logs (last 100 lines)
            $lines = explode("\n", $logs);
            $recent_lines = array_slice($lines, -100);
            
            foreach ($recent_lines as $line) {
                if (empty(trim($line))) continue;
                
                $class = '';
                if (strpos($line, 'AUI Debug') !== false) {
                    $class = 'aui-log';
                } elseif (strpos($line, 'ERROR') !== false) {
                    $class = 'error';
                } elseif (strpos($line, 'SUCCESS') !== false) {
                    $class = 'success';
                }
                
                echo '<div class="' . $class . '">' . htmlspecialchars($line) . '</div>';
            }
        } else {
            echo '<div>No debug log file found at: ' . $log_file . '</div>';
        }
        ?>
    </div>
    
    <script>
        function clearLogs() {
            if (confirm('Are you sure you want to clear all logs?')) {
                fetch('?view=logs&action=clear', {method: 'POST'})
                .then(() => location.reload());
            }
        }
        
        // Auto refresh every 5 seconds
        setTimeout(() => location.reload(), 5000);
    </script>
</body>
</html>

<?php
// Handle clear logs action
if (isset($_GET['action']) && $_GET['action'] === 'clear') {
    if (file_exists($log_file)) {
        file_put_contents($log_file, '');
    }
    exit('Logs cleared');
}
?>
