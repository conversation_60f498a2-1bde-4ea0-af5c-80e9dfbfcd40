<?php

namespace Bo<PERSON>ble\Menu\Providers;

use Bo<PERSON>ble\Base\Events\DeletedContentEvent;
use Bo<PERSON>ble\Menu\Listeners\DeleteMenuNodeListener;
use Bo<PERSON>ble\Menu\Listeners\UpdateMenuNodeUrlListener;
use Bo<PERSON>ble\Slug\Events\UpdatedSlugEvent;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    protected $listen = [
        UpdatedSlugEvent::class => [
            UpdateMenuNodeUrlListener::class,
        ],
        DeletedContentEvent::class => [
            DeleteMenuNodeListener::class,
        ],
    ];
}
