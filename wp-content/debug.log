[01-Jul-2025 11:05:14 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::hasChildren($allow_links = true) should either be compatible with RecursiveDirectoryIterator::hasChildren(bool $allowLinks = false): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 57
[01-Jul-2025 11:05:14 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::rewind() should either be compatible with FilesystemIterator::rewind(): void, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 35
[01-Jul-2025 11:05:14 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::next() should either be compatible with DirectoryIterator::next(): void, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 42
[01-Jul-2025 11:05:14 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Extension_Filter::getChildren() should either be compatible with RecursiveFilterIterator::getChildren(): ?RecursiveFilterIterator, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\filter\class-ai1wm-recursive-extension-filter.php on line 47
[01-Jul-2025 11:05:14 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Extension_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\filter\class-ai1wm-recursive-extension-filter.php on line 37
[01-Jul-2025 11:05:14 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Exclude_Filter::getChildren() should either be compatible with RecursiveFilterIterator::getChildren(): ?RecursiveFilterIterator, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\filter\class-ai1wm-recursive-exclude-filter.php on line 41
[01-Jul-2025 11:05:14 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Exclude_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\filter\class-ai1wm-recursive-exclude-filter.php on line 37
[01-Jul-2025 11:05:14 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\filter\class-ai1wm-recursive-newline-filter.php on line 28
[01-Jul-2025 11:05:14 UTC] AUI Hook: toplevel_page_auto-upload-image
[01-Jul-2025 11:05:14 UTC] AUI Scripts enqueued for hook: toplevel_page_auto-upload-image
[01-Jul-2025 11:05:15 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::hasChildren($allow_links = true) should either be compatible with RecursiveDirectoryIterator::hasChildren(bool $allowLinks = false): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 57
[01-Jul-2025 11:05:15 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::rewind() should either be compatible with FilesystemIterator::rewind(): void, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 35
[01-Jul-2025 11:05:15 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::next() should either be compatible with DirectoryIterator::next(): void, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 42
[01-Jul-2025 11:05:15 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Extension_Filter::getChildren() should either be compatible with RecursiveFilterIterator::getChildren(): ?RecursiveFilterIterator, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\filter\class-ai1wm-recursive-extension-filter.php on line 47
[01-Jul-2025 11:05:15 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Extension_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\filter\class-ai1wm-recursive-extension-filter.php on line 37
[01-Jul-2025 11:05:15 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Exclude_Filter::getChildren() should either be compatible with RecursiveFilterIterator::getChildren(): ?RecursiveFilterIterator, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\filter\class-ai1wm-recursive-exclude-filter.php on line 41
[01-Jul-2025 11:05:15 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Exclude_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\filter\class-ai1wm-recursive-exclude-filter.php on line 37
[01-Jul-2025 11:05:15 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\filter\class-ai1wm-recursive-newline-filter.php on line 28
[01-Jul-2025 11:05:25 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::hasChildren($allow_links = true) should either be compatible with RecursiveDirectoryIterator::hasChildren(bool $allowLinks = false): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 57
[01-Jul-2025 11:05:25 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::rewind() should either be compatible with FilesystemIterator::rewind(): void, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 35
[01-Jul-2025 11:05:25 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::next() should either be compatible with DirectoryIterator::next(): void, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 42
[01-Jul-2025 11:05:25 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Extension_Filter::getChildren() should either be compatible with RecursiveFilterIterator::getChildren(): ?RecursiveFilterIterator, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\filter\class-ai1wm-recursive-extension-filter.php on line 47
[01-Jul-2025 11:05:25 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Extension_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\filter\class-ai1wm-recursive-extension-filter.php on line 37
[01-Jul-2025 11:05:25 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Exclude_Filter::getChildren() should either be compatible with RecursiveFilterIterator::getChildren(): ?RecursiveFilterIterator, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\filter\class-ai1wm-recursive-exclude-filter.php on line 41
[01-Jul-2025 11:05:25 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Exclude_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\filter\class-ai1wm-recursive-exclude-filter.php on line 37
[01-Jul-2025 11:05:25 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\filter\class-ai1wm-recursive-newline-filter.php on line 28
[01-Jul-2025 11:05:25 UTC] AUI Debug - process_uploaded_files called with 2 files
[01-Jul-2025 11:05:25 UTC] AUI Debug - File #0: ban-chai.png
[01-Jul-2025 11:05:25 UTC] AUI Debug - File #1: ban-chai_1.png
[01-Jul-2025 11:05:25 UTC] AUI Debug - Processing upload #0:
[01-Jul-2025 11:05:25 UTC] AUI Debug - Original filename: ban-chai.png
[01-Jul-2025 11:05:25 UTC] AUI Debug - Sanitized filename: ban-chai.png
[01-Jul-2025 11:05:25 UTC] AUI Debug - Original filename: ban-chai.png
[01-Jul-2025 11:05:25 UTC] AUI Debug - Unique filename: ban-chai_1751367925_0_208.png
[01-Jul-2025 11:05:25 UTC] AUI Debug - Temp path: C:\laragon\www\reup/wp-content/uploads/aui-temp/ban-chai_1751367925_0_208.png
[01-Jul-2025 11:05:25 UTC] AUI Debug - Successfully moved file to: C:\laragon\www\reup/wp-content/uploads/aui-temp/ban-chai_1751367925_0_208.png
[01-Jul-2025 11:05:25 UTC] AUI Debug - File ID: file_0_1751367925_2247, Original: ban-chai.png, Temp: ban-chai_1751367925_0_208.png
[01-Jul-2025 11:05:25 UTC] AUI Debug - Processing upload #1:
[01-Jul-2025 11:05:25 UTC] AUI Debug - Original filename: ban-chai_1.png
[01-Jul-2025 11:05:25 UTC] AUI Debug - Sanitized filename: ban-chai_1.png
[01-Jul-2025 11:05:25 UTC] AUI Debug - Original filename: ban-chai_1.png
[01-Jul-2025 11:05:25 UTC] AUI Debug - Unique filename: ban-chai_1_1751367925_1_281.png
[01-Jul-2025 11:05:25 UTC] AUI Debug - Temp path: C:\laragon\www\reup/wp-content/uploads/aui-temp/ban-chai_1_1751367925_1_281.png
[01-Jul-2025 11:05:25 UTC] AUI Debug - Successfully moved file to: C:\laragon\www\reup/wp-content/uploads/aui-temp/ban-chai_1_1751367925_1_281.png
[01-Jul-2025 11:05:25 UTC] AUI Debug - File ID: file_1_1751367925_9002, Original: ban-chai_1.png, Temp: ban-chai_1_1751367925_1_281.png
[01-Jul-2025 11:05:25 UTC] AUI Debug - Files after sorting:
[01-Jul-2025 11:05:25 UTC] AUI Debug - Sorted file: ban-chai.png
[01-Jul-2025 11:05:25 UTC] AUI Debug - Sorted file: ban-chai_1.png
[01-Jul-2025 11:05:25 UTC] AUI Debug - extract_base_name: 'ban-chai' -> no pattern, using full name
[01-Jul-2025 11:05:25 UTC] AUI Debug - Processing file: ban-chai.png (ID: file_0_1751367925_2247), base: ban-chai
[01-Jul-2025 11:05:25 UTC] AUI Debug - Added file file_0_1751367925_2247 to current group (base: ban-chai)
[01-Jul-2025 11:05:25 UTC] AUI Debug - extract_base_name: 'ban-chai_1' -> base: 'ban-chai', number: '1'
[01-Jul-2025 11:05:25 UTC] AUI Debug - Processing file: ban-chai_1.png (ID: file_1_1751367925_9002), base: ban-chai
[01-Jul-2025 11:05:25 UTC] AUI Debug - Added file file_1_1751367925_9002 to current group (base: ban-chai)
[01-Jul-2025 11:05:25 UTC] AUI Debug - Added final group with 2 files
[01-Jul-2025 11:05:25 UTC] AUI Debug - No simple number pattern in 'ban-chai', using 0
[01-Jul-2025 11:05:25 UTC] AUI Debug - Extracted number 1 from 'ban-chai_1'
[01-Jul-2025 11:05:25 UTC] AUI Debug - Group 0 final order:
[01-Jul-2025 11:05:25 UTC] AUI Debug - ban-chai.png
[01-Jul-2025 11:05:25 UTC] AUI Debug - ban-chai_1.png
[01-Jul-2025 11:05:25 UTC] AUI Debug - Final grouped images:
[01-Jul-2025 11:05:25 UTC] AUI Debug - Group 0 (2 files): ban-chai.png, ban-chai_1.png
[01-Jul-2025 11:05:25 UTC] AUI Debug - Total groups: 1
[01-Jul-2025 11:05:25 UTC] AUI Debug - Original files count: 2
[01-Jul-2025 11:05:25 UTC] AUI Debug - Total files in groups: 2
[01-Jul-2025 11:05:25 UTC] AUI Debug - Starting upload for group 0 with 2 files
[01-Jul-2025 11:05:25 UTC] AUI Debug - Uploading group of 2 files to domain: https://cn-av.com
[01-Jul-2025 11:05:25 UTC] AUI Debug - Group file #0: ban-chai.png (path: C:\laragon\www\reup/wp-content/uploads/aui-temp/ban-chai_1751367925_0_208.png)
[01-Jul-2025 11:05:25 UTC] AUI Debug - Group file #1: ban-chai_1.png (path: C:\laragon\www\reup/wp-content/uploads/aui-temp/ban-chai_1_1751367925_1_281.png)
[01-Jul-2025 11:05:25 UTC] AUI Debug - Starting upload file #0: ban-chai.png (ID: file_0_1751367925_2247)
[01-Jul-2025 11:05:25 UTC] AUI Debug - File path: C:\laragon\www\reup/wp-content/uploads/aui-temp/ban-chai_1751367925_0_208.png
[01-Jul-2025 11:05:25 UTC] AUI Debug - File URL: http://reup.test/wp-content/uploads/aui-temp/ban-chai_1751367925_0_208.png
[01-Jul-2025 11:05:25 UTC] AUI Debug - upload_single_image called for: ban-chai.png
[01-Jul-2025 11:05:25 UTC] AUI Debug - File path: C:\laragon\www\reup/wp-content/uploads/aui-temp/ban-chai_1751367925_0_208.png
[01-Jul-2025 11:05:25 UTC] AUI Debug - File URL: http://reup.test/wp-content/uploads/aui-temp/ban-chai_1751367925_0_208.png
[01-Jul-2025 11:05:25 UTC] AUI Debug - File identifier: file_0_1751367925_2247
[01-Jul-2025 11:05:25 UTC] AUI Debug - File ban-chai.png (ID: file_0_1751367925_2247) is NEW, proceeding with upload
[01-Jul-2025 11:05:25 UTC] AUI Debug - Current tracker keys: 
[01-Jul-2025 11:05:25 UTC] AUI Debug - Original filename: ban-chai.png
[01-Jul-2025 11:05:25 UTC] AUI Debug - Name without ext: ban-chai
[01-Jul-2025 11:05:25 UTC] AUI Debug - API name: ban-chai
[01-Jul-2025 11:05:25 UTC] AUI Debug - Extension: png
[01-Jul-2025 11:05:25 UTC] AUI Debug - API data prepared: {"upload_auto_delete":0,"type":"image\/png","nameFile":"ban-chai","extFile":"png","linkImage":"http:\/\/reup.test\/wp-content\/uploads\/aui-temp\/ban-chai_1751367925_0_208.png","size":385141}
[01-Jul-2025 11:05:25 UTC] AUI Debug - API Failed for ban-chai.png: HTTP 403: <!DOCTYPE html>
<!--[if lt IE 7]> <html class="no-js ie6 oldie" lang="en-US"> <![endif]-->
<!--[if IE 7]>    <html class="no-js ie7 oldie" lang="en-US"> <![endif]-->
<!--[if IE 8]>    <html class="no-js ie8 oldie" lang="en-US"> <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js" lang="en-US"> <!--<![endif]-->
<head>
<title>Attention Required! | Cloudflare</title>
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta http-equiv="X-UA-Compatible" content="IE=Edge" />
<meta name="robots" content="noindex, nofollow" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<link rel="stylesheet" id="cf_styles-css" href="/cdn-cgi/styles/cf.errors.css" />
<!--[if lt IE 9]><link rel="stylesheet" id='cf_styles-ie-css' href="/cdn-cgi/styles/cf.errors.ie.css" /><![endif]-->
<style>body{margin:0;padding:0}</style>


<!--[if gte IE 10]><!-->
<script>
  if (!navigator.cookieEnabled) {
    window.addEventListener('DOMContentLoaded', function () {
      var cookieEl = document.getElementById('cookie-alert');
      cookieEl.style.display = 'block';
    })
  }
</script>
<!--<![endif]-->

</head>
<body>
  <div id="cf-wrapper">
    <div class="cf-alert cf-alert-error cf-cookie-error" id="cookie-alert" data-translate="enable_cookies">Please enable cookies.</div>
    <div id="cf-error-details" class="cf-error-details-wrapper">
      <div class="cf-wrapper cf-header cf-error-overview">
        <h1 data-translate="block_headline">Sorry, you have been blocked</h1>
        <h2 class="cf-subheadline"><span data-translate="unable_to_access">You are unable to access</span> cn-av.com</h2>
      </div><!-- /.header -->

      <div class="cf-section cf-highlight">
        <div class="cf-wrapper">
          <div class="cf-screenshot-container cf-screenshot-full">
            
              <span class="cf-no-screenshot error"></span>
            
          </div>
        </div>
      </div><!-- /.captcha-container -->

      <div class="cf-section cf-wrapper">
        <div class="cf-columns two">
          <div class="cf-column">
            <h2 data-translate="blocked_why_headline">Why have I been blocked?</h2>

            <p data-translate="blocked_why_detail">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.</p>
          </div>

          <div class="cf-column">
            <h2 data-translate="blocked_resolve_headline">What can I do to resolve this?</h2>

            <p data-translate="blocked_resolve_detail">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.</p>
          </div>
        </div>
      </div><!-- /.section -->

      <div class="cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300">
    <p class="text-13">
      <span class="cf-footer-item sm:block sm:mb-1">Cloudflare Ray ID: <strong class="font-semibold">95856dad3ae2ce39</strong></span>
      <span class="cf-footer-separator sm:hidden">&bull;</span>
      <span id="cf-footer-item-ip" class="cf-footer-item hidden sm:block sm:mb-1">
        Your IP:
        <button type="button" id="cf-footer-ip-reveal" class="cf-footer-ip-reveal-btn">Click to reveal</button>
        <span class="hidden" id="cf-footer-ip">*************</span>
        <span class="cf-footer-separator sm:hidden">&bull;</span>
      </span>
      <span class="cf-footer-item sm:block sm:mb-1"><span>Performance &amp; security by</span> <a rel="noopener noreferrer" href="https://www.cloudflare.com/5xx-error-landing" id="brand_link" target="_blank">Cloudflare</a></span>
      
    </p>
    <script>(function(){function d(){var b=a.getElementById("cf-footer-item-ip"),c=a.getElementById("cf-footer-ip-reveal");b&&"classList"in b&&(b.classList.remove("hidden"),c.addEventListener("click",function(){c.classList.add("hidden");a.getElementById("cf-footer-ip").classList.remove("hidden")}))}var a=document;document.addEventListener&&a.addEventListener("DOMContentLoaded",d)})();</script>
  </div><!-- /.error-footer -->

    </div><!-- /#cf-error-details -->
  </div><!-- /#cf-wrapper -->

  <script>
    window._cf_translation = {};
    
    
  </script>
</body>
</html>
[01-Jul-2025 11:05:26 UTC] AUI Debug - API Failed for ban-chai.png: HTTP 403: <!DOCTYPE html>
<!--[if lt IE 7]> <html class="no-js ie6 oldie" lang="en-US"> <![endif]-->
<!--[if IE 7]>    <html class="no-js ie7 oldie" lang="en-US"> <![endif]-->
<!--[if IE 8]>    <html class="no-js ie8 oldie" lang="en-US"> <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js" lang="en-US"> <!--<![endif]-->
<head>
<title>Attention Required! | Cloudflare</title>
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta http-equiv="X-UA-Compatible" content="IE=Edge" />
<meta name="robots" content="noindex, nofollow" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<link rel="stylesheet" id="cf_styles-css" href="/cdn-cgi/styles/cf.errors.css" />
<!--[if lt IE 9]><link rel="stylesheet" id='cf_styles-ie-css' href="/cdn-cgi/styles/cf.errors.ie.css" /><![endif]-->
<style>body{margin:0;padding:0}</style>


<!--[if gte IE 10]><!-->
<script>
  if (!navigator.cookieEnabled) {
    window.addEventListener('DOMContentLoaded', function () {
      var cookieEl = document.getElementById('cookie-alert');
      cookieEl.style.display = 'block';
    })
  }
</script>
<!--<![endif]-->

</head>
<body>
  <div id="cf-wrapper">
    <div class="cf-alert cf-alert-error cf-cookie-error" id="cookie-alert" data-translate="enable_cookies">Please enable cookies.</div>
    <div id="cf-error-details" class="cf-error-details-wrapper">
      <div class="cf-wrapper cf-header cf-error-overview">
        <h1 data-translate="block_headline">Sorry, you have been blocked</h1>
        <h2 class="cf-subheadline"><span data-translate="unable_to_access">You are unable to access</span> cn-av.com</h2>
      </div><!-- /.header -->

      <div class="cf-section cf-highlight">
        <div class="cf-wrapper">
          <div class="cf-screenshot-container cf-screenshot-full">
            
              <span class="cf-no-screenshot error"></span>
            
          </div>
        </div>
      </div><!-- /.captcha-container -->

      <div class="cf-section cf-wrapper">
        <div class="cf-columns two">
          <div class="cf-column">
            <h2 data-translate="blocked_why_headline">Why have I been blocked?</h2>

            <p data-translate="blocked_why_detail">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.</p>
          </div>

          <div class="cf-column">
            <h2 data-translate="blocked_resolve_headline">What can I do to resolve this?</h2>

            <p data-translate="blocked_resolve_detail">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.</p>
          </div>
        </div>
      </div><!-- /.section -->

      <div class="cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300">
    <p class="text-13">
      <span class="cf-footer-item sm:block sm:mb-1">Cloudflare Ray ID: <strong class="font-semibold">95856db46ca8ab43</strong></span>
      <span class="cf-footer-separator sm:hidden">&bull;</span>
      <span id="cf-footer-item-ip" class="cf-footer-item hidden sm:block sm:mb-1">
        Your IP:
        <button type="button" id="cf-footer-ip-reveal" class="cf-footer-ip-reveal-btn">Click to reveal</button>
        <span class="hidden" id="cf-footer-ip">*************</span>
        <span class="cf-footer-separator sm:hidden">&bull;</span>
      </span>
      <span class="cf-footer-item sm:block sm:mb-1"><span>Performance &amp; security by</span> <a rel="noopener noreferrer" href="https://www.cloudflare.com/5xx-error-landing" id="brand_link" target="_blank">Cloudflare</a></span>
      
    </p>
    <script>(function(){function d(){var b=a.getElementById("cf-footer-item-ip"),c=a.getElementById("cf-footer-ip-reveal");b&&"classList"in b&&(b.classList.remove("hidden"),c.addEventListener("click",function(){c.classList.add("hidden");a.getElementById("cf-footer-ip").classList.remove("hidden")}))}var a=document;document.addEventListener&&a.addEventListener("DOMContentLoaded",d)})();</script>
  </div><!-- /.error-footer -->

    </div><!-- /#cf-error-details -->
  </div><!-- /#cf-wrapper -->

  <script>
    window._cf_translation = {};
    
    
  </script>
</body>
</html>
[01-Jul-2025 11:05:28 UTC] AUI Debug - API Failed for ban-chai.png: HTTP 403: <!DOCTYPE html>
<!--[if lt IE 7]> <html class="no-js ie6 oldie" lang="en-US"> <![endif]-->
<!--[if IE 7]>    <html class="no-js ie7 oldie" lang="en-US"> <![endif]-->
<!--[if IE 8]>    <html class="no-js ie8 oldie" lang="en-US"> <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js" lang="en-US"> <!--<![endif]-->
<head>
<title>Attention Required! | Cloudflare</title>
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta http-equiv="X-UA-Compatible" content="IE=Edge" />
<meta name="robots" content="noindex, nofollow" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<link rel="stylesheet" id="cf_styles-css" href="/cdn-cgi/styles/cf.errors.css" />
<!--[if lt IE 9]><link rel="stylesheet" id='cf_styles-ie-css' href="/cdn-cgi/styles/cf.errors.ie.css" /><![endif]-->
<style>body{margin:0;padding:0}</style>


<!--[if gte IE 10]><!-->
<script>
  if (!navigator.cookieEnabled) {
    window.addEventListener('DOMContentLoaded', function () {
      var cookieEl = document.getElementById('cookie-alert');
      cookieEl.style.display = 'block';
    })
  }
</script>
<!--<![endif]-->

</head>
<body>
  <div id="cf-wrapper">
    <div class="cf-alert cf-alert-error cf-cookie-error" id="cookie-alert" data-translate="enable_cookies">Please enable cookies.</div>
    <div id="cf-error-details" class="cf-error-details-wrapper">
      <div class="cf-wrapper cf-header cf-error-overview">
        <h1 data-translate="block_headline">Sorry, you have been blocked</h1>
        <h2 class="cf-subheadline"><span data-translate="unable_to_access">You are unable to access</span> cn-av.com</h2>
      </div><!-- /.header -->

      <div class="cf-section cf-highlight">
        <div class="cf-wrapper">
          <div class="cf-screenshot-container cf-screenshot-full">
            
              <span class="cf-no-screenshot error"></span>
            
          </div>
        </div>
      </div><!-- /.captcha-container -->

      <div class="cf-section cf-wrapper">
        <div class="cf-columns two">
          <div class="cf-column">
            <h2 data-translate="blocked_why_headline">Why have I been blocked?</h2>

            <p data-translate="blocked_why_detail">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.</p>
          </div>

          <div class="cf-column">
            <h2 data-translate="blocked_resolve_headline">What can I do to resolve this?</h2>

            <p data-translate="blocked_resolve_detail">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.</p>
          </div>
        </div>
      </div><!-- /.section -->

      <div class="cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300">
    <p class="text-13">
      <span class="cf-footer-item sm:block sm:mb-1">Cloudflare Ray ID: <strong class="font-semibold">95856dbb89eb243f</strong></span>
      <span class="cf-footer-separator sm:hidden">&bull;</span>
      <span id="cf-footer-item-ip" class="cf-footer-item hidden sm:block sm:mb-1">
        Your IP:
        <button type="button" id="cf-footer-ip-reveal" class="cf-footer-ip-reveal-btn">Click to reveal</button>
        <span class="hidden" id="cf-footer-ip">*************</span>
        <span class="cf-footer-separator sm:hidden">&bull;</span>
      </span>
      <span class="cf-footer-item sm:block sm:mb-1"><span>Performance &amp; security by</span> <a rel="noopener noreferrer" href="https://www.cloudflare.com/5xx-error-landing" id="brand_link" target="_blank">Cloudflare</a></span>
      
    </p>
    <script>(function(){function d(){var b=a.getElementById("cf-footer-item-ip"),c=a.getElementById("cf-footer-ip-reveal");b&&"classList"in b&&(b.classList.remove("hidden"),c.addEventListener("click",function(){c.classList.add("hidden");a.getElementById("cf-footer-ip").classList.remove("hidden")}))}var a=document;document.addEventListener&&a.addEventListener("DOMContentLoaded",d)})();</script>
  </div><!-- /.error-footer -->

    </div><!-- /#cf-error-details -->
  </div><!-- /#cf-wrapper -->

  <script>
    window._cf_translation = {};
    
    
  </script>
</body>
</html>
[01-Jul-2025 11:05:28 UTC] AUI Debug - Upload completed for ban-chai.png: FAILED
[01-Jul-2025 11:05:28 UTC] AUI Debug - Starting upload file #1: ban-chai_1.png (ID: file_1_1751367925_9002)
[01-Jul-2025 11:05:28 UTC] AUI Debug - File path: C:\laragon\www\reup/wp-content/uploads/aui-temp/ban-chai_1_1751367925_1_281.png
[01-Jul-2025 11:05:28 UTC] AUI Debug - File URL: http://reup.test/wp-content/uploads/aui-temp/ban-chai_1_1751367925_1_281.png
[01-Jul-2025 11:05:29 UTC] AUI Debug - upload_single_image called for: ban-chai_1.png
[01-Jul-2025 11:05:29 UTC] AUI Debug - File path: C:\laragon\www\reup/wp-content/uploads/aui-temp/ban-chai_1_1751367925_1_281.png
[01-Jul-2025 11:05:29 UTC] AUI Debug - File URL: http://reup.test/wp-content/uploads/aui-temp/ban-chai_1_1751367925_1_281.png
[01-Jul-2025 11:05:29 UTC] AUI Debug - File identifier: file_1_1751367925_9002
[01-Jul-2025 11:05:29 UTC] AUI Debug - File ban-chai_1.png (ID: file_1_1751367925_9002) is NEW, proceeding with upload
[01-Jul-2025 11:05:29 UTC] AUI Debug - Current tracker keys: file_0_1751367925_2247
[01-Jul-2025 11:05:29 UTC] AUI Debug - Original filename: ban-chai_1.png
[01-Jul-2025 11:05:29 UTC] AUI Debug - Name without ext: ban-chai_1
[01-Jul-2025 11:05:29 UTC] AUI Debug - API name: ban-chai_1
[01-Jul-2025 11:05:29 UTC] AUI Debug - Extension: png
[01-Jul-2025 11:05:29 UTC] AUI Debug - API data prepared: {"upload_auto_delete":0,"type":"image\/png","nameFile":"ban-chai_1","extFile":"png","linkImage":"http:\/\/reup.test\/wp-content\/uploads\/aui-temp\/ban-chai_1_1751367925_1_281.png","size":598799}
[01-Jul-2025 11:05:29 UTC] AUI Debug - API Failed for ban-chai_1.png: HTTP 403: <!DOCTYPE html>
<!--[if lt IE 7]> <html class="no-js ie6 oldie" lang="en-US"> <![endif]-->
<!--[if IE 7]>    <html class="no-js ie7 oldie" lang="en-US"> <![endif]-->
<!--[if IE 8]>    <html class="no-js ie8 oldie" lang="en-US"> <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js" lang="en-US"> <!--<![endif]-->
<head>
<title>Attention Required! | Cloudflare</title>
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta http-equiv="X-UA-Compatible" content="IE=Edge" />
<meta name="robots" content="noindex, nofollow" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<link rel="stylesheet" id="cf_styles-css" href="/cdn-cgi/styles/cf.errors.css" />
<!--[if lt IE 9]><link rel="stylesheet" id='cf_styles-ie-css' href="/cdn-cgi/styles/cf.errors.ie.css" /><![endif]-->
<style>body{margin:0;padding:0}</style>


<!--[if gte IE 10]><!-->
<script>
  if (!navigator.cookieEnabled) {
    window.addEventListener('DOMContentLoaded', function () {
      var cookieEl = document.getElementById('cookie-alert');
      cookieEl.style.display = 'block';
    })
  }
</script>
<!--<![endif]-->

</head>
<body>
  <div id="cf-wrapper">
    <div class="cf-alert cf-alert-error cf-cookie-error" id="cookie-alert" data-translate="enable_cookies">Please enable cookies.</div>
    <div id="cf-error-details" class="cf-error-details-wrapper">
      <div class="cf-wrapper cf-header cf-error-overview">
        <h1 data-translate="block_headline">Sorry, you have been blocked</h1>
        <h2 class="cf-subheadline"><span data-translate="unable_to_access">You are unable to access</span> cn-av.com</h2>
      </div><!-- /.header -->

      <div class="cf-section cf-highlight">
        <div class="cf-wrapper">
          <div class="cf-screenshot-container cf-screenshot-full">
            
              <span class="cf-no-screenshot error"></span>
            
          </div>
        </div>
      </div><!-- /.captcha-container -->

      <div class="cf-section cf-wrapper">
        <div class="cf-columns two">
          <div class="cf-column">
            <h2 data-translate="blocked_why_headline">Why have I been blocked?</h2>

            <p data-translate="blocked_why_detail">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.</p>
          </div>

          <div class="cf-column">
            <h2 data-translate="blocked_resolve_headline">What can I do to resolve this?</h2>

            <p data-translate="blocked_resolve_detail">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.</p>
          </div>
        </div>
      </div><!-- /.section -->

      <div class="cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300">
    <p class="text-13">
      <span class="cf-footer-item sm:block sm:mb-1">Cloudflare Ray ID: <strong class="font-semibold">95856dc2cbd9fceb</strong></span>
      <span class="cf-footer-separator sm:hidden">&bull;</span>
      <span id="cf-footer-item-ip" class="cf-footer-item hidden sm:block sm:mb-1">
        Your IP:
        <button type="button" id="cf-footer-ip-reveal" class="cf-footer-ip-reveal-btn">Click to reveal</button>
        <span class="hidden" id="cf-footer-ip">*************</span>
        <span class="cf-footer-separator sm:hidden">&bull;</span>
      </span>
      <span class="cf-footer-item sm:block sm:mb-1"><span>Performance &amp; security by</span> <a rel="noopener noreferrer" href="https://www.cloudflare.com/5xx-error-landing" id="brand_link" target="_blank">Cloudflare</a></span>
      
    </p>
    <script>(function(){function d(){var b=a.getElementById("cf-footer-item-ip"),c=a.getElementById("cf-footer-ip-reveal");b&&"classList"in b&&(b.classList.remove("hidden"),c.addEventListener("click",function(){c.classList.add("hidden");a.getElementById("cf-footer-ip").classList.remove("hidden")}))}var a=document;document.addEventListener&&a.addEventListener("DOMContentLoaded",d)})();</script>
  </div><!-- /.error-footer -->

    </div><!-- /#cf-error-details -->
  </div><!-- /#cf-wrapper -->

  <script>
    window._cf_translation = {};
    
    
  </script>
</body>
</html>
[01-Jul-2025 11:05:30 UTC] AUI Debug - API Failed for ban-chai_1.png: HTTP 403: <!DOCTYPE html>
<!--[if lt IE 7]> <html class="no-js ie6 oldie" lang="en-US"> <![endif]-->
<!--[if IE 7]>    <html class="no-js ie7 oldie" lang="en-US"> <![endif]-->
<!--[if IE 8]>    <html class="no-js ie8 oldie" lang="en-US"> <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js" lang="en-US"> <!--<![endif]-->
<head>
<title>Attention Required! | Cloudflare</title>
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta http-equiv="X-UA-Compatible" content="IE=Edge" />
<meta name="robots" content="noindex, nofollow" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<link rel="stylesheet" id="cf_styles-css" href="/cdn-cgi/styles/cf.errors.css" />
<!--[if lt IE 9]><link rel="stylesheet" id='cf_styles-ie-css' href="/cdn-cgi/styles/cf.errors.ie.css" /><![endif]-->
<style>body{margin:0;padding:0}</style>


<!--[if gte IE 10]><!-->
<script>
  if (!navigator.cookieEnabled) {
    window.addEventListener('DOMContentLoaded', function () {
      var cookieEl = document.getElementById('cookie-alert');
      cookieEl.style.display = 'block';
    })
  }
</script>
<!--<![endif]-->

</head>
<body>
  <div id="cf-wrapper">
    <div class="cf-alert cf-alert-error cf-cookie-error" id="cookie-alert" data-translate="enable_cookies">Please enable cookies.</div>
    <div id="cf-error-details" class="cf-error-details-wrapper">
      <div class="cf-wrapper cf-header cf-error-overview">
        <h1 data-translate="block_headline">Sorry, you have been blocked</h1>
        <h2 class="cf-subheadline"><span data-translate="unable_to_access">You are unable to access</span> cn-av.com</h2>
      </div><!-- /.header -->

      <div class="cf-section cf-highlight">
        <div class="cf-wrapper">
          <div class="cf-screenshot-container cf-screenshot-full">
            
              <span class="cf-no-screenshot error"></span>
            
          </div>
        </div>
      </div><!-- /.captcha-container -->

      <div class="cf-section cf-wrapper">
        <div class="cf-columns two">
          <div class="cf-column">
            <h2 data-translate="blocked_why_headline">Why have I been blocked?</h2>

            <p data-translate="blocked_why_detail">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.</p>
          </div>

          <div class="cf-column">
            <h2 data-translate="blocked_resolve_headline">What can I do to resolve this?</h2>

            <p data-translate="blocked_resolve_detail">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.</p>
          </div>
        </div>
      </div><!-- /.section -->

      <div class="cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300">
    <p class="text-13">
      <span class="cf-footer-item sm:block sm:mb-1">Cloudflare Ray ID: <strong class="font-semibold">95856dca0b6d44c3</strong></span>
      <span class="cf-footer-separator sm:hidden">&bull;</span>
      <span id="cf-footer-item-ip" class="cf-footer-item hidden sm:block sm:mb-1">
        Your IP:
        <button type="button" id="cf-footer-ip-reveal" class="cf-footer-ip-reveal-btn">Click to reveal</button>
        <span class="hidden" id="cf-footer-ip">*************</span>
        <span class="cf-footer-separator sm:hidden">&bull;</span>
      </span>
      <span class="cf-footer-item sm:block sm:mb-1"><span>Performance &amp; security by</span> <a rel="noopener noreferrer" href="https://www.cloudflare.com/5xx-error-landing" id="brand_link" target="_blank">Cloudflare</a></span>
      
    </p>
    <script>(function(){function d(){var b=a.getElementById("cf-footer-item-ip"),c=a.getElementById("cf-footer-ip-reveal");b&&"classList"in b&&(b.classList.remove("hidden"),c.addEventListener("click",function(){c.classList.add("hidden");a.getElementById("cf-footer-ip").classList.remove("hidden")}))}var a=document;document.addEventListener&&a.addEventListener("DOMContentLoaded",d)})();</script>
  </div><!-- /.error-footer -->

    </div><!-- /#cf-error-details -->
  </div><!-- /#cf-wrapper -->

  <script>
    window._cf_translation = {};
    
    
  </script>
</body>
</html>
[01-Jul-2025 11:05:31 UTC] AUI Debug - API Failed for ban-chai_1.png: HTTP 403: <!DOCTYPE html>
<!--[if lt IE 7]> <html class="no-js ie6 oldie" lang="en-US"> <![endif]-->
<!--[if IE 7]>    <html class="no-js ie7 oldie" lang="en-US"> <![endif]-->
<!--[if IE 8]>    <html class="no-js ie8 oldie" lang="en-US"> <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js" lang="en-US"> <!--<![endif]-->
<head>
<title>Attention Required! | Cloudflare</title>
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta http-equiv="X-UA-Compatible" content="IE=Edge" />
<meta name="robots" content="noindex, nofollow" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<link rel="stylesheet" id="cf_styles-css" href="/cdn-cgi/styles/cf.errors.css" />
<!--[if lt IE 9]><link rel="stylesheet" id='cf_styles-ie-css' href="/cdn-cgi/styles/cf.errors.ie.css" /><![endif]-->
<style>body{margin:0;padding:0}</style>


<!--[if gte IE 10]><!-->
<script>
  if (!navigator.cookieEnabled) {
    window.addEventListener('DOMContentLoaded', function () {
      var cookieEl = document.getElementById('cookie-alert');
      cookieEl.style.display = 'block';
    })
  }
</script>
<!--<![endif]-->

</head>
<body>
  <div id="cf-wrapper">
    <div class="cf-alert cf-alert-error cf-cookie-error" id="cookie-alert" data-translate="enable_cookies">Please enable cookies.</div>
    <div id="cf-error-details" class="cf-error-details-wrapper">
      <div class="cf-wrapper cf-header cf-error-overview">
        <h1 data-translate="block_headline">Sorry, you have been blocked</h1>
        <h2 class="cf-subheadline"><span data-translate="unable_to_access">You are unable to access</span> cn-av.com</h2>
      </div><!-- /.header -->

      <div class="cf-section cf-highlight">
        <div class="cf-wrapper">
          <div class="cf-screenshot-container cf-screenshot-full">
            
              <span class="cf-no-screenshot error"></span>
            
          </div>
        </div>
      </div><!-- /.captcha-container -->

      <div class="cf-section cf-wrapper">
        <div class="cf-columns two">
          <div class="cf-column">
            <h2 data-translate="blocked_why_headline">Why have I been blocked?</h2>

            <p data-translate="blocked_why_detail">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.</p>
          </div>

          <div class="cf-column">
            <h2 data-translate="blocked_resolve_headline">What can I do to resolve this?</h2>

            <p data-translate="blocked_resolve_detail">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.</p>
          </div>
        </div>
      </div><!-- /.section -->

      <div class="cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300">
    <p class="text-13">
      <span class="cf-footer-item sm:block sm:mb-1">Cloudflare Ray ID: <strong class="font-semibold">95856dd13d51603b</strong></span>
      <span class="cf-footer-separator sm:hidden">&bull;</span>
      <span id="cf-footer-item-ip" class="cf-footer-item hidden sm:block sm:mb-1">
        Your IP:
        <button type="button" id="cf-footer-ip-reveal" class="cf-footer-ip-reveal-btn">Click to reveal</button>
        <span class="hidden" id="cf-footer-ip">*************</span>
        <span class="cf-footer-separator sm:hidden">&bull;</span>
      </span>
      <span class="cf-footer-item sm:block sm:mb-1"><span>Performance &amp; security by</span> <a rel="noopener noreferrer" href="https://www.cloudflare.com/5xx-error-landing" id="brand_link" target="_blank">Cloudflare</a></span>
      
    </p>
    <script>(function(){function d(){var b=a.getElementById("cf-footer-item-ip"),c=a.getElementById("cf-footer-ip-reveal");b&&"classList"in b&&(b.classList.remove("hidden"),c.addEventListener("click",function(){c.classList.add("hidden");a.getElementById("cf-footer-ip").classList.remove("hidden")}))}var a=document;document.addEventListener&&a.addEventListener("DOMContentLoaded",d)})();</script>
  </div><!-- /.error-footer -->

    </div><!-- /#cf-error-details -->
  </div><!-- /#cf-wrapper -->

  <script>
    window._cf_translation = {};
    
    
  </script>
</body>
</html>
[01-Jul-2025 11:05:31 UTC] AUI Debug - Upload completed for ban-chai_1.png: FAILED
[01-Jul-2025 11:05:31 UTC] AUI Debug - Group upload completed. Total results: 2
[01-Jul-2025 11:05:31 UTC] AUI Debug - Group result #0: ban-chai.png (ID: file_0_1751367925_2247) - FAILED
[01-Jul-2025 11:05:31 UTC] AUI Debug - Group result #1: ban-chai_1.png (ID: file_1_1751367925_9002) - FAILED
[01-Jul-2025 11:05:31 UTC] AUI Debug - Group 0 upload completed, got 2 results
[01-Jul-2025 11:05:31 UTC] AUI Debug - Result for ban-chai.png (ID: file_0_1751367925_2247): FAILED
[01-Jul-2025 11:05:31 UTC] AUI Debug - Result for ban-chai_1.png (ID: file_1_1751367925_9002): FAILED
[01-Jul-2025 11:05:31 UTC] AUI Debug - Added result for ban-chai.png (ID: file_0_1751367925_2247)
[01-Jul-2025 11:05:31 UTC] AUI Debug - DUPLICATE DETECTED! Skipping result for ban-chai.png (ID: file_0_1751367925_2247)
[01-Jul-2025 11:05:31 UTC] AUI Debug - Total results: 1
[01-Jul-2025 11:05:31 UTC] AUI Debug - Successful: 0
[01-Jul-2025 11:05:31 UTC] AUI Debug - Failed: 1
[01-Jul-2025 11:05:31 UTC] AUI Debug - Final results being sent to client:
[01-Jul-2025 11:05:31 UTC] AUI Debug - Final result #0: ban-chai.png (ID: file_0_1751367925_2247)
[01-Jul-2025 11:06:15 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::hasChildren($allow_links = true) should either be compatible with RecursiveDirectoryIterator::hasChildren(bool $allowLinks = false): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 57
[01-Jul-2025 11:06:15 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::rewind() should either be compatible with FilesystemIterator::rewind(): void, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 35
[01-Jul-2025 11:06:15 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::next() should either be compatible with DirectoryIterator::next(): void, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 42
[01-Jul-2025 11:06:15 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Extension_Filter::getChildren() should either be compatible with RecursiveFilterIterator::getChildren(): ?RecursiveFilterIterator, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\filter\class-ai1wm-recursive-extension-filter.php on line 47
[01-Jul-2025 11:06:15 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Extension_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\filter\class-ai1wm-recursive-extension-filter.php on line 37
[01-Jul-2025 11:06:15 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Exclude_Filter::getChildren() should either be compatible with RecursiveFilterIterator::getChildren(): ?RecursiveFilterIterator, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\filter\class-ai1wm-recursive-exclude-filter.php on line 41
[01-Jul-2025 11:06:15 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Exclude_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\filter\class-ai1wm-recursive-exclude-filter.php on line 37
[01-Jul-2025 11:06:15 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\filter\class-ai1wm-recursive-newline-filter.php on line 28
[01-Jul-2025 11:06:20 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::hasChildren($allow_links = true) should either be compatible with RecursiveDirectoryIterator::hasChildren(bool $allowLinks = false): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 57
[01-Jul-2025 11:06:20 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::rewind() should either be compatible with FilesystemIterator::rewind(): void, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 35
[01-Jul-2025 11:06:20 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::next() should either be compatible with DirectoryIterator::next(): void, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 42
[01-Jul-2025 11:06:20 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Extension_Filter::getChildren() should either be compatible with RecursiveFilterIterator::getChildren(): ?RecursiveFilterIterator, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\filter\class-ai1wm-recursive-extension-filter.php on line 47
[01-Jul-2025 11:06:20 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Extension_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\filter\class-ai1wm-recursive-extension-filter.php on line 37
[01-Jul-2025 11:06:20 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Exclude_Filter::getChildren() should either be compatible with RecursiveFilterIterator::getChildren(): ?RecursiveFilterIterator, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\filter\class-ai1wm-recursive-exclude-filter.php on line 41
[01-Jul-2025 11:06:20 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Exclude_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\filter\class-ai1wm-recursive-exclude-filter.php on line 37
[01-Jul-2025 11:06:20 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\filter\class-ai1wm-recursive-newline-filter.php on line 28
[01-Jul-2025 11:06:20 UTC] AUI Hook: toplevel_page_auto-upload-image
[01-Jul-2025 11:06:20 UTC] AUI Scripts enqueued for hook: toplevel_page_auto-upload-image
[01-Jul-2025 11:06:20 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::hasChildren($allow_links = true) should either be compatible with RecursiveDirectoryIterator::hasChildren(bool $allowLinks = false): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 57
[01-Jul-2025 11:06:20 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::rewind() should either be compatible with FilesystemIterator::rewind(): void, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 35
[01-Jul-2025 11:06:20 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::next() should either be compatible with DirectoryIterator::next(): void, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 42
[01-Jul-2025 11:06:20 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Extension_Filter::getChildren() should either be compatible with RecursiveFilterIterator::getChildren(): ?RecursiveFilterIterator, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\filter\class-ai1wm-recursive-extension-filter.php on line 47
[01-Jul-2025 11:06:20 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Extension_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\filter\class-ai1wm-recursive-extension-filter.php on line 37
[01-Jul-2025 11:06:20 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Exclude_Filter::getChildren() should either be compatible with RecursiveFilterIterator::getChildren(): ?RecursiveFilterIterator, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\filter\class-ai1wm-recursive-exclude-filter.php on line 41
[01-Jul-2025 11:06:20 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Exclude_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\filter\class-ai1wm-recursive-exclude-filter.php on line 37
[01-Jul-2025 11:06:20 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\filter\class-ai1wm-recursive-newline-filter.php on line 28
[01-Jul-2025 11:06:26 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::hasChildren($allow_links = true) should either be compatible with RecursiveDirectoryIterator::hasChildren(bool $allowLinks = false): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 57
[01-Jul-2025 11:06:26 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::rewind() should either be compatible with FilesystemIterator::rewind(): void, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 35
[01-Jul-2025 11:06:26 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::next() should either be compatible with DirectoryIterator::next(): void, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 42
[01-Jul-2025 11:06:26 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Extension_Filter::getChildren() should either be compatible with RecursiveFilterIterator::getChildren(): ?RecursiveFilterIterator, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\filter\class-ai1wm-recursive-extension-filter.php on line 47
[01-Jul-2025 11:06:26 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Extension_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\filter\class-ai1wm-recursive-extension-filter.php on line 37
[01-Jul-2025 11:06:26 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Exclude_Filter::getChildren() should either be compatible with RecursiveFilterIterator::getChildren(): ?RecursiveFilterIterator, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\filter\class-ai1wm-recursive-exclude-filter.php on line 41
[01-Jul-2025 11:06:26 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Exclude_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\filter\class-ai1wm-recursive-exclude-filter.php on line 37
[01-Jul-2025 11:06:26 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\filter\class-ai1wm-recursive-newline-filter.php on line 28
[01-Jul-2025 11:06:26 UTC] AUI Debug - process_uploaded_files called with 2 files
[01-Jul-2025 11:06:26 UTC] AUI Debug - File #0: ban-chai.png
[01-Jul-2025 11:06:26 UTC] AUI Debug - File #1: ban-chai_1.png
[01-Jul-2025 11:06:26 UTC] AUI Debug - Processing upload #0:
[01-Jul-2025 11:06:26 UTC] AUI Debug - Original filename: ban-chai.png
[01-Jul-2025 11:06:26 UTC] AUI Debug - Sanitized filename: ban-chai.png
[01-Jul-2025 11:06:26 UTC] AUI Debug - Original filename: ban-chai.png
[01-Jul-2025 11:06:26 UTC] AUI Debug - Unique filename: ban-chai_1751367986_0_712.png
[01-Jul-2025 11:06:26 UTC] AUI Debug - Temp path: C:\laragon\www\reup/wp-content/uploads/aui-temp/ban-chai_1751367986_0_712.png
[01-Jul-2025 11:06:26 UTC] AUI Debug - Successfully moved file to: C:\laragon\www\reup/wp-content/uploads/aui-temp/ban-chai_1751367986_0_712.png
[01-Jul-2025 11:06:26 UTC] AUI Debug - File ID: file_0_1751367986_5413, Original: ban-chai.png, Temp: ban-chai_1751367986_0_712.png
[01-Jul-2025 11:06:26 UTC] AUI Debug - Processing upload #1:
[01-Jul-2025 11:06:26 UTC] AUI Debug - Original filename: ban-chai_1.png
[01-Jul-2025 11:06:26 UTC] AUI Debug - Sanitized filename: ban-chai_1.png
[01-Jul-2025 11:06:26 UTC] AUI Debug - Original filename: ban-chai_1.png
[01-Jul-2025 11:06:26 UTC] AUI Debug - Unique filename: ban-chai_1_1751367986_1_198.png
[01-Jul-2025 11:06:26 UTC] AUI Debug - Temp path: C:\laragon\www\reup/wp-content/uploads/aui-temp/ban-chai_1_1751367986_1_198.png
[01-Jul-2025 11:06:26 UTC] AUI Debug - Successfully moved file to: C:\laragon\www\reup/wp-content/uploads/aui-temp/ban-chai_1_1751367986_1_198.png
[01-Jul-2025 11:06:26 UTC] AUI Debug - File ID: file_1_1751367986_5434, Original: ban-chai_1.png, Temp: ban-chai_1_1751367986_1_198.png
[01-Jul-2025 11:06:26 UTC] AUI Debug - Files after sorting:
[01-Jul-2025 11:06:26 UTC] AUI Debug - Sorted file: ban-chai.png
[01-Jul-2025 11:06:26 UTC] AUI Debug - Sorted file: ban-chai_1.png
[01-Jul-2025 11:06:26 UTC] AUI Debug - extract_base_name: 'ban-chai' -> no pattern, using full name
[01-Jul-2025 11:06:26 UTC] AUI Debug - Processing file: ban-chai.png (ID: file_0_1751367986_5413), base: ban-chai
[01-Jul-2025 11:06:26 UTC] AUI Debug - Added file file_0_1751367986_5413 to current group (base: ban-chai)
[01-Jul-2025 11:06:26 UTC] AUI Debug - extract_base_name: 'ban-chai_1' -> base: 'ban-chai', number: '1'
[01-Jul-2025 11:06:26 UTC] AUI Debug - Processing file: ban-chai_1.png (ID: file_1_1751367986_5434), base: ban-chai
[01-Jul-2025 11:06:26 UTC] AUI Debug - Added file file_1_1751367986_5434 to current group (base: ban-chai)
[01-Jul-2025 11:06:26 UTC] AUI Debug - Added final group with 2 files
[01-Jul-2025 11:06:26 UTC] AUI Debug - No simple number pattern in 'ban-chai', using 0
[01-Jul-2025 11:06:26 UTC] AUI Debug - Extracted number 1 from 'ban-chai_1'
[01-Jul-2025 11:06:26 UTC] AUI Debug - Group 0 final order:
[01-Jul-2025 11:06:26 UTC] AUI Debug - ban-chai.png
[01-Jul-2025 11:06:26 UTC] AUI Debug - ban-chai_1.png
[01-Jul-2025 11:06:26 UTC] AUI Debug - Final grouped images:
[01-Jul-2025 11:06:26 UTC] AUI Debug - Group 0 (2 files): ban-chai.png, ban-chai_1.png
[01-Jul-2025 11:06:26 UTC] AUI Debug - Total groups: 1
[01-Jul-2025 11:06:26 UTC] AUI Debug - Original files count: 2
[01-Jul-2025 11:06:26 UTC] AUI Debug - Total files in groups: 2
[01-Jul-2025 11:06:26 UTC] AUI Debug - Starting upload for group 0 with 2 files
[01-Jul-2025 11:06:26 UTC] AUI Debug - Uploading group of 2 files to domain: https://cn-av.com
[01-Jul-2025 11:06:26 UTC] AUI Debug - Group file #0: ban-chai.png (path: C:\laragon\www\reup/wp-content/uploads/aui-temp/ban-chai_1751367986_0_712.png)
[01-Jul-2025 11:06:26 UTC] AUI Debug - Group file #1: ban-chai_1.png (path: C:\laragon\www\reup/wp-content/uploads/aui-temp/ban-chai_1_1751367986_1_198.png)
[01-Jul-2025 11:06:26 UTC] AUI Debug - Starting upload file #0: ban-chai.png (ID: file_0_1751367986_5413)
[01-Jul-2025 11:06:26 UTC] AUI Debug - File path: C:\laragon\www\reup/wp-content/uploads/aui-temp/ban-chai_1751367986_0_712.png
[01-Jul-2025 11:06:26 UTC] AUI Debug - File URL: http://reup.test/wp-content/uploads/aui-temp/ban-chai_1751367986_0_712.png
[01-Jul-2025 11:06:26 UTC] AUI Debug - upload_single_image called for: ban-chai.png
[01-Jul-2025 11:06:26 UTC] AUI Debug - File path: C:\laragon\www\reup/wp-content/uploads/aui-temp/ban-chai_1751367986_0_712.png
[01-Jul-2025 11:06:26 UTC] AUI Debug - File URL: http://reup.test/wp-content/uploads/aui-temp/ban-chai_1751367986_0_712.png
[01-Jul-2025 11:06:26 UTC] AUI Debug - File identifier: file_0_1751367986_5413
[01-Jul-2025 11:06:26 UTC] AUI Debug - File ban-chai.png (ID: file_0_1751367986_5413) is NEW, proceeding with upload
[01-Jul-2025 11:06:26 UTC] AUI Debug - Current tracker keys: 
[01-Jul-2025 11:06:26 UTC] AUI Debug - Original filename: ban-chai.png
[01-Jul-2025 11:06:26 UTC] AUI Debug - Name without ext: ban-chai
[01-Jul-2025 11:06:26 UTC] AUI Debug - API name: ban-chai
[01-Jul-2025 11:06:26 UTC] AUI Debug - Extension: png
[01-Jul-2025 11:06:26 UTC] AUI Debug - API data prepared: {"upload_auto_delete":0,"type":"image\/png","nameFile":"ban-chai","extFile":"png","linkImage":"http:\/\/reup.test\/wp-content\/uploads\/aui-temp\/ban-chai_1751367986_0_712.png","size":385141}
[01-Jul-2025 11:06:27 UTC] AUI Debug - API Failed for ban-chai.png: HTTP 403: <!DOCTYPE html>
<!--[if lt IE 7]> <html class="no-js ie6 oldie" lang="en-US"> <![endif]-->
<!--[if IE 7]>    <html class="no-js ie7 oldie" lang="en-US"> <![endif]-->
<!--[if IE 8]>    <html class="no-js ie8 oldie" lang="en-US"> <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js" lang="en-US"> <!--<![endif]-->
<head>
<title>Attention Required! | Cloudflare</title>
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta http-equiv="X-UA-Compatible" content="IE=Edge" />
<meta name="robots" content="noindex, nofollow" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<link rel="stylesheet" id="cf_styles-css" href="/cdn-cgi/styles/cf.errors.css" />
<!--[if lt IE 9]><link rel="stylesheet" id='cf_styles-ie-css' href="/cdn-cgi/styles/cf.errors.ie.css" /><![endif]-->
<style>body{margin:0;padding:0}</style>


<!--[if gte IE 10]><!-->
<script>
  if (!navigator.cookieEnabled) {
    window.addEventListener('DOMContentLoaded', function () {
      var cookieEl = document.getElementById('cookie-alert');
      cookieEl.style.display = 'block';
    })
  }
</script>
<!--<![endif]-->

</head>
<body>
  <div id="cf-wrapper">
    <div class="cf-alert cf-alert-error cf-cookie-error" id="cookie-alert" data-translate="enable_cookies">Please enable cookies.</div>
    <div id="cf-error-details" class="cf-error-details-wrapper">
      <div class="cf-wrapper cf-header cf-error-overview">
        <h1 data-translate="block_headline">Sorry, you have been blocked</h1>
        <h2 class="cf-subheadline"><span data-translate="unable_to_access">You are unable to access</span> cn-av.com</h2>
      </div><!-- /.header -->

      <div class="cf-section cf-highlight">
        <div class="cf-wrapper">
          <div class="cf-screenshot-container cf-screenshot-full">
            
              <span class="cf-no-screenshot error"></span>
            
          </div>
        </div>
      </div><!-- /.captcha-container -->

      <div class="cf-section cf-wrapper">
        <div class="cf-columns two">
          <div class="cf-column">
            <h2 data-translate="blocked_why_headline">Why have I been blocked?</h2>

            <p data-translate="blocked_why_detail">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.</p>
          </div>

          <div class="cf-column">
            <h2 data-translate="blocked_resolve_headline">What can I do to resolve this?</h2>

            <p data-translate="blocked_resolve_detail">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.</p>
          </div>
        </div>
      </div><!-- /.section -->

      <div class="cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300">
    <p class="text-13">
      <span class="cf-footer-item sm:block sm:mb-1">Cloudflare Ray ID: <strong class="font-semibold">95856f2c7bf25f5f</strong></span>
      <span class="cf-footer-separator sm:hidden">&bull;</span>
      <span id="cf-footer-item-ip" class="cf-footer-item hidden sm:block sm:mb-1">
        Your IP:
        <button type="button" id="cf-footer-ip-reveal" class="cf-footer-ip-reveal-btn">Click to reveal</button>
        <span class="hidden" id="cf-footer-ip">*************</span>
        <span class="cf-footer-separator sm:hidden">&bull;</span>
      </span>
      <span class="cf-footer-item sm:block sm:mb-1"><span>Performance &amp; security by</span> <a rel="noopener noreferrer" href="https://www.cloudflare.com/5xx-error-landing" id="brand_link" target="_blank">Cloudflare</a></span>
      
    </p>
    <script>(function(){function d(){var b=a.getElementById("cf-footer-item-ip"),c=a.getElementById("cf-footer-ip-reveal");b&&"classList"in b&&(b.classList.remove("hidden"),c.addEventListener("click",function(){c.classList.add("hidden");a.getElementById("cf-footer-ip").classList.remove("hidden")}))}var a=document;document.addEventListener&&a.addEventListener("DOMContentLoaded",d)})();</script>
  </div><!-- /.error-footer -->

    </div><!-- /#cf-error-details -->
  </div><!-- /#cf-wrapper -->

  <script>
    window._cf_translation = {};
    
    
  </script>
</body>
</html>
[01-Jul-2025 11:06:28 UTC] AUI Debug - API Failed for ban-chai.png: HTTP 403: <!DOCTYPE html>
<!--[if lt IE 7]> <html class="no-js ie6 oldie" lang="en-US"> <![endif]-->
<!--[if IE 7]>    <html class="no-js ie7 oldie" lang="en-US"> <![endif]-->
<!--[if IE 8]>    <html class="no-js ie8 oldie" lang="en-US"> <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js" lang="en-US"> <!--<![endif]-->
<head>
<title>Attention Required! | Cloudflare</title>
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta http-equiv="X-UA-Compatible" content="IE=Edge" />
<meta name="robots" content="noindex, nofollow" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<link rel="stylesheet" id="cf_styles-css" href="/cdn-cgi/styles/cf.errors.css" />
<!--[if lt IE 9]><link rel="stylesheet" id='cf_styles-ie-css' href="/cdn-cgi/styles/cf.errors.ie.css" /><![endif]-->
<style>body{margin:0;padding:0}</style>


<!--[if gte IE 10]><!-->
<script>
  if (!navigator.cookieEnabled) {
    window.addEventListener('DOMContentLoaded', function () {
      var cookieEl = document.getElementById('cookie-alert');
      cookieEl.style.display = 'block';
    })
  }
</script>
<!--<![endif]-->

</head>
<body>
  <div id="cf-wrapper">
    <div class="cf-alert cf-alert-error cf-cookie-error" id="cookie-alert" data-translate="enable_cookies">Please enable cookies.</div>
    <div id="cf-error-details" class="cf-error-details-wrapper">
      <div class="cf-wrapper cf-header cf-error-overview">
        <h1 data-translate="block_headline">Sorry, you have been blocked</h1>
        <h2 class="cf-subheadline"><span data-translate="unable_to_access">You are unable to access</span> cn-av.com</h2>
      </div><!-- /.header -->

      <div class="cf-section cf-highlight">
        <div class="cf-wrapper">
          <div class="cf-screenshot-container cf-screenshot-full">
            
              <span class="cf-no-screenshot error"></span>
            
          </div>
        </div>
      </div><!-- /.captcha-container -->

      <div class="cf-section cf-wrapper">
        <div class="cf-columns two">
          <div class="cf-column">
            <h2 data-translate="blocked_why_headline">Why have I been blocked?</h2>

            <p data-translate="blocked_why_detail">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.</p>
          </div>

          <div class="cf-column">
            <h2 data-translate="blocked_resolve_headline">What can I do to resolve this?</h2>

            <p data-translate="blocked_resolve_detail">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.</p>
          </div>
        </div>
      </div><!-- /.section -->

      <div class="cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300">
    <p class="text-13">
      <span class="cf-footer-item sm:block sm:mb-1">Cloudflare Ray ID: <strong class="font-semibold">95856f33ba7b243f</strong></span>
      <span class="cf-footer-separator sm:hidden">&bull;</span>
      <span id="cf-footer-item-ip" class="cf-footer-item hidden sm:block sm:mb-1">
        Your IP:
        <button type="button" id="cf-footer-ip-reveal" class="cf-footer-ip-reveal-btn">Click to reveal</button>
        <span class="hidden" id="cf-footer-ip">*************</span>
        <span class="cf-footer-separator sm:hidden">&bull;</span>
      </span>
      <span class="cf-footer-item sm:block sm:mb-1"><span>Performance &amp; security by</span> <a rel="noopener noreferrer" href="https://www.cloudflare.com/5xx-error-landing" id="brand_link" target="_blank">Cloudflare</a></span>
      
    </p>
    <script>(function(){function d(){var b=a.getElementById("cf-footer-item-ip"),c=a.getElementById("cf-footer-ip-reveal");b&&"classList"in b&&(b.classList.remove("hidden"),c.addEventListener("click",function(){c.classList.add("hidden");a.getElementById("cf-footer-ip").classList.remove("hidden")}))}var a=document;document.addEventListener&&a.addEventListener("DOMContentLoaded",d)})();</script>
  </div><!-- /.error-footer -->

    </div><!-- /#cf-error-details -->
  </div><!-- /#cf-wrapper -->

  <script>
    window._cf_translation = {};
    
    
  </script>
</body>
</html>
[01-Jul-2025 11:06:29 UTC] AUI Debug - API Failed for ban-chai.png: HTTP 403: <!DOCTYPE html>
<!--[if lt IE 7]> <html class="no-js ie6 oldie" lang="en-US"> <![endif]-->
<!--[if IE 7]>    <html class="no-js ie7 oldie" lang="en-US"> <![endif]-->
<!--[if IE 8]>    <html class="no-js ie8 oldie" lang="en-US"> <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js" lang="en-US"> <!--<![endif]-->
<head>
<title>Attention Required! | Cloudflare</title>
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta http-equiv="X-UA-Compatible" content="IE=Edge" />
<meta name="robots" content="noindex, nofollow" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<link rel="stylesheet" id="cf_styles-css" href="/cdn-cgi/styles/cf.errors.css" />
<!--[if lt IE 9]><link rel="stylesheet" id='cf_styles-ie-css' href="/cdn-cgi/styles/cf.errors.ie.css" /><![endif]-->
<style>body{margin:0;padding:0}</style>


<!--[if gte IE 10]><!-->
<script>
  if (!navigator.cookieEnabled) {
    window.addEventListener('DOMContentLoaded', function () {
      var cookieEl = document.getElementById('cookie-alert');
      cookieEl.style.display = 'block';
    })
  }
</script>
<!--<![endif]-->

</head>
<body>
  <div id="cf-wrapper">
    <div class="cf-alert cf-alert-error cf-cookie-error" id="cookie-alert" data-translate="enable_cookies">Please enable cookies.</div>
    <div id="cf-error-details" class="cf-error-details-wrapper">
      <div class="cf-wrapper cf-header cf-error-overview">
        <h1 data-translate="block_headline">Sorry, you have been blocked</h1>
        <h2 class="cf-subheadline"><span data-translate="unable_to_access">You are unable to access</span> cn-av.com</h2>
      </div><!-- /.header -->

      <div class="cf-section cf-highlight">
        <div class="cf-wrapper">
          <div class="cf-screenshot-container cf-screenshot-full">
            
              <span class="cf-no-screenshot error"></span>
            
          </div>
        </div>
      </div><!-- /.captcha-container -->

      <div class="cf-section cf-wrapper">
        <div class="cf-columns two">
          <div class="cf-column">
            <h2 data-translate="blocked_why_headline">Why have I been blocked?</h2>

            <p data-translate="blocked_why_detail">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.</p>
          </div>

          <div class="cf-column">
            <h2 data-translate="blocked_resolve_headline">What can I do to resolve this?</h2>

            <p data-translate="blocked_resolve_detail">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.</p>
          </div>
        </div>
      </div><!-- /.section -->

      <div class="cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300">
    <p class="text-13">
      <span class="cf-footer-item sm:block sm:mb-1">Cloudflare Ray ID: <strong class="font-semibold">95856f3abcd15f3a</strong></span>
      <span class="cf-footer-separator sm:hidden">&bull;</span>
      <span id="cf-footer-item-ip" class="cf-footer-item hidden sm:block sm:mb-1">
        Your IP:
        <button type="button" id="cf-footer-ip-reveal" class="cf-footer-ip-reveal-btn">Click to reveal</button>
        <span class="hidden" id="cf-footer-ip">*************</span>
        <span class="cf-footer-separator sm:hidden">&bull;</span>
      </span>
      <span class="cf-footer-item sm:block sm:mb-1"><span>Performance &amp; security by</span> <a rel="noopener noreferrer" href="https://www.cloudflare.com/5xx-error-landing" id="brand_link" target="_blank">Cloudflare</a></span>
      
    </p>
    <script>(function(){function d(){var b=a.getElementById("cf-footer-item-ip"),c=a.getElementById("cf-footer-ip-reveal");b&&"classList"in b&&(b.classList.remove("hidden"),c.addEventListener("click",function(){c.classList.add("hidden");a.getElementById("cf-footer-ip").classList.remove("hidden")}))}var a=document;document.addEventListener&&a.addEventListener("DOMContentLoaded",d)})();</script>
  </div><!-- /.error-footer -->

    </div><!-- /#cf-error-details -->
  </div><!-- /#cf-wrapper -->

  <script>
    window._cf_translation = {};
    
    
  </script>
</body>
</html>
[01-Jul-2025 11:06:29 UTC] AUI Debug - Upload completed for ban-chai.png: FAILED
[01-Jul-2025 11:06:29 UTC] AUI Debug - Starting upload file #1: ban-chai_1.png (ID: file_1_1751367986_5434)
[01-Jul-2025 11:06:29 UTC] AUI Debug - File path: C:\laragon\www\reup/wp-content/uploads/aui-temp/ban-chai_1_1751367986_1_198.png
[01-Jul-2025 11:06:29 UTC] AUI Debug - File URL: http://reup.test/wp-content/uploads/aui-temp/ban-chai_1_1751367986_1_198.png
[01-Jul-2025 11:06:30 UTC] AUI Debug - upload_single_image called for: ban-chai_1.png
[01-Jul-2025 11:06:30 UTC] AUI Debug - File path: C:\laragon\www\reup/wp-content/uploads/aui-temp/ban-chai_1_1751367986_1_198.png
[01-Jul-2025 11:06:30 UTC] AUI Debug - File URL: http://reup.test/wp-content/uploads/aui-temp/ban-chai_1_1751367986_1_198.png
[01-Jul-2025 11:06:30 UTC] AUI Debug - File identifier: file_1_1751367986_5434
[01-Jul-2025 11:06:30 UTC] AUI Debug - File ban-chai_1.png (ID: file_1_1751367986_5434) is NEW, proceeding with upload
[01-Jul-2025 11:06:30 UTC] AUI Debug - Current tracker keys: file_0_1751367986_5413
[01-Jul-2025 11:06:30 UTC] AUI Debug - Original filename: ban-chai_1.png
[01-Jul-2025 11:06:30 UTC] AUI Debug - Name without ext: ban-chai_1
[01-Jul-2025 11:06:30 UTC] AUI Debug - API name: ban-chai_1
[01-Jul-2025 11:06:30 UTC] AUI Debug - Extension: png
[01-Jul-2025 11:06:30 UTC] AUI Debug - API data prepared: {"upload_auto_delete":0,"type":"image\/png","nameFile":"ban-chai_1","extFile":"png","linkImage":"http:\/\/reup.test\/wp-content\/uploads\/aui-temp\/ban-chai_1_1751367986_1_198.png","size":598799}
[01-Jul-2025 11:06:30 UTC] AUI Debug - API Failed for ban-chai_1.png: HTTP 403: <!DOCTYPE html>
<!--[if lt IE 7]> <html class="no-js ie6 oldie" lang="en-US"> <![endif]-->
<!--[if IE 7]>    <html class="no-js ie7 oldie" lang="en-US"> <![endif]-->
<!--[if IE 8]>    <html class="no-js ie8 oldie" lang="en-US"> <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js" lang="en-US"> <!--<![endif]-->
<head>
<title>Attention Required! | Cloudflare</title>
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta http-equiv="X-UA-Compatible" content="IE=Edge" />
<meta name="robots" content="noindex, nofollow" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<link rel="stylesheet" id="cf_styles-css" href="/cdn-cgi/styles/cf.errors.css" />
<!--[if lt IE 9]><link rel="stylesheet" id='cf_styles-ie-css' href="/cdn-cgi/styles/cf.errors.ie.css" /><![endif]-->
<style>body{margin:0;padding:0}</style>


<!--[if gte IE 10]><!-->
<script>
  if (!navigator.cookieEnabled) {
    window.addEventListener('DOMContentLoaded', function () {
      var cookieEl = document.getElementById('cookie-alert');
      cookieEl.style.display = 'block';
    })
  }
</script>
<!--<![endif]-->

</head>
<body>
  <div id="cf-wrapper">
    <div class="cf-alert cf-alert-error cf-cookie-error" id="cookie-alert" data-translate="enable_cookies">Please enable cookies.</div>
    <div id="cf-error-details" class="cf-error-details-wrapper">
      <div class="cf-wrapper cf-header cf-error-overview">
        <h1 data-translate="block_headline">Sorry, you have been blocked</h1>
        <h2 class="cf-subheadline"><span data-translate="unable_to_access">You are unable to access</span> cn-av.com</h2>
      </div><!-- /.header -->

      <div class="cf-section cf-highlight">
        <div class="cf-wrapper">
          <div class="cf-screenshot-container cf-screenshot-full">
            
              <span class="cf-no-screenshot error"></span>
            
          </div>
        </div>
      </div><!-- /.captcha-container -->

      <div class="cf-section cf-wrapper">
        <div class="cf-columns two">
          <div class="cf-column">
            <h2 data-translate="blocked_why_headline">Why have I been blocked?</h2>

            <p data-translate="blocked_why_detail">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.</p>
          </div>

          <div class="cf-column">
            <h2 data-translate="blocked_resolve_headline">What can I do to resolve this?</h2>

            <p data-translate="blocked_resolve_detail">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.</p>
          </div>
        </div>
      </div><!-- /.section -->

      <div class="cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300">
    <p class="text-13">
      <span class="cf-footer-item sm:block sm:mb-1">Cloudflare Ray ID: <strong class="font-semibold">95856f421e85fe0f</strong></span>
      <span class="cf-footer-separator sm:hidden">&bull;</span>
      <span id="cf-footer-item-ip" class="cf-footer-item hidden sm:block sm:mb-1">
        Your IP:
        <button type="button" id="cf-footer-ip-reveal" class="cf-footer-ip-reveal-btn">Click to reveal</button>
        <span class="hidden" id="cf-footer-ip">*************</span>
        <span class="cf-footer-separator sm:hidden">&bull;</span>
      </span>
      <span class="cf-footer-item sm:block sm:mb-1"><span>Performance &amp; security by</span> <a rel="noopener noreferrer" href="https://www.cloudflare.com/5xx-error-landing" id="brand_link" target="_blank">Cloudflare</a></span>
      
    </p>
    <script>(function(){function d(){var b=a.getElementById("cf-footer-item-ip"),c=a.getElementById("cf-footer-ip-reveal");b&&"classList"in b&&(b.classList.remove("hidden"),c.addEventListener("click",function(){c.classList.add("hidden");a.getElementById("cf-footer-ip").classList.remove("hidden")}))}var a=document;document.addEventListener&&a.addEventListener("DOMContentLoaded",d)})();</script>
  </div><!-- /.error-footer -->

    </div><!-- /#cf-error-details -->
  </div><!-- /#cf-wrapper -->

  <script>
    window._cf_translation = {};
    
    
  </script>
</body>
</html>
[01-Jul-2025 11:06:31 UTC] AUI Debug - API Failed for ban-chai_1.png: HTTP 403: <!DOCTYPE html>
<!--[if lt IE 7]> <html class="no-js ie6 oldie" lang="en-US"> <![endif]-->
<!--[if IE 7]>    <html class="no-js ie7 oldie" lang="en-US"> <![endif]-->
<!--[if IE 8]>    <html class="no-js ie8 oldie" lang="en-US"> <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js" lang="en-US"> <!--<![endif]-->
<head>
<title>Attention Required! | Cloudflare</title>
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta http-equiv="X-UA-Compatible" content="IE=Edge" />
<meta name="robots" content="noindex, nofollow" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<link rel="stylesheet" id="cf_styles-css" href="/cdn-cgi/styles/cf.errors.css" />
<!--[if lt IE 9]><link rel="stylesheet" id='cf_styles-ie-css' href="/cdn-cgi/styles/cf.errors.ie.css" /><![endif]-->
<style>body{margin:0;padding:0}</style>


<!--[if gte IE 10]><!-->
<script>
  if (!navigator.cookieEnabled) {
    window.addEventListener('DOMContentLoaded', function () {
      var cookieEl = document.getElementById('cookie-alert');
      cookieEl.style.display = 'block';
    })
  }
</script>
<!--<![endif]-->

</head>
<body>
  <div id="cf-wrapper">
    <div class="cf-alert cf-alert-error cf-cookie-error" id="cookie-alert" data-translate="enable_cookies">Please enable cookies.</div>
    <div id="cf-error-details" class="cf-error-details-wrapper">
      <div class="cf-wrapper cf-header cf-error-overview">
        <h1 data-translate="block_headline">Sorry, you have been blocked</h1>
        <h2 class="cf-subheadline"><span data-translate="unable_to_access">You are unable to access</span> cn-av.com</h2>
      </div><!-- /.header -->

      <div class="cf-section cf-highlight">
        <div class="cf-wrapper">
          <div class="cf-screenshot-container cf-screenshot-full">
            
              <span class="cf-no-screenshot error"></span>
            
          </div>
        </div>
      </div><!-- /.captcha-container -->

      <div class="cf-section cf-wrapper">
        <div class="cf-columns two">
          <div class="cf-column">
            <h2 data-translate="blocked_why_headline">Why have I been blocked?</h2>

            <p data-translate="blocked_why_detail">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.</p>
          </div>

          <div class="cf-column">
            <h2 data-translate="blocked_resolve_headline">What can I do to resolve this?</h2>

            <p data-translate="blocked_resolve_detail">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.</p>
          </div>
        </div>
      </div><!-- /.section -->

      <div class="cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300">
    <p class="text-13">
      <span class="cf-footer-item sm:block sm:mb-1">Cloudflare Ray ID: <strong class="font-semibold">95856f495afff888</strong></span>
      <span class="cf-footer-separator sm:hidden">&bull;</span>
      <span id="cf-footer-item-ip" class="cf-footer-item hidden sm:block sm:mb-1">
        Your IP:
        <button type="button" id="cf-footer-ip-reveal" class="cf-footer-ip-reveal-btn">Click to reveal</button>
        <span class="hidden" id="cf-footer-ip">*************</span>
        <span class="cf-footer-separator sm:hidden">&bull;</span>
      </span>
      <span class="cf-footer-item sm:block sm:mb-1"><span>Performance &amp; security by</span> <a rel="noopener noreferrer" href="https://www.cloudflare.com/5xx-error-landing" id="brand_link" target="_blank">Cloudflare</a></span>
      
    </p>
    <script>(function(){function d(){var b=a.getElementById("cf-footer-item-ip"),c=a.getElementById("cf-footer-ip-reveal");b&&"classList"in b&&(b.classList.remove("hidden"),c.addEventListener("click",function(){c.classList.add("hidden");a.getElementById("cf-footer-ip").classList.remove("hidden")}))}var a=document;document.addEventListener&&a.addEventListener("DOMContentLoaded",d)})();</script>
  </div><!-- /.error-footer -->

    </div><!-- /#cf-error-details -->
  </div><!-- /#cf-wrapper -->

  <script>
    window._cf_translation = {};
    
    
  </script>
</body>
</html>
[01-Jul-2025 11:06:32 UTC] AUI Debug - API Failed for ban-chai_1.png: HTTP 403: <!DOCTYPE html>
<!--[if lt IE 7]> <html class="no-js ie6 oldie" lang="en-US"> <![endif]-->
<!--[if IE 7]>    <html class="no-js ie7 oldie" lang="en-US"> <![endif]-->
<!--[if IE 8]>    <html class="no-js ie8 oldie" lang="en-US"> <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js" lang="en-US"> <!--<![endif]-->
<head>
<title>Attention Required! | Cloudflare</title>
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta http-equiv="X-UA-Compatible" content="IE=Edge" />
<meta name="robots" content="noindex, nofollow" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<link rel="stylesheet" id="cf_styles-css" href="/cdn-cgi/styles/cf.errors.css" />
<!--[if lt IE 9]><link rel="stylesheet" id='cf_styles-ie-css' href="/cdn-cgi/styles/cf.errors.ie.css" /><![endif]-->
<style>body{margin:0;padding:0}</style>


<!--[if gte IE 10]><!-->
<script>
  if (!navigator.cookieEnabled) {
    window.addEventListener('DOMContentLoaded', function () {
      var cookieEl = document.getElementById('cookie-alert');
      cookieEl.style.display = 'block';
    })
  }
</script>
<!--<![endif]-->

</head>
<body>
  <div id="cf-wrapper">
    <div class="cf-alert cf-alert-error cf-cookie-error" id="cookie-alert" data-translate="enable_cookies">Please enable cookies.</div>
    <div id="cf-error-details" class="cf-error-details-wrapper">
      <div class="cf-wrapper cf-header cf-error-overview">
        <h1 data-translate="block_headline">Sorry, you have been blocked</h1>
        <h2 class="cf-subheadline"><span data-translate="unable_to_access">You are unable to access</span> cn-av.com</h2>
      </div><!-- /.header -->

      <div class="cf-section cf-highlight">
        <div class="cf-wrapper">
          <div class="cf-screenshot-container cf-screenshot-full">
            
              <span class="cf-no-screenshot error"></span>
            
          </div>
        </div>
      </div><!-- /.captcha-container -->

      <div class="cf-section cf-wrapper">
        <div class="cf-columns two">
          <div class="cf-column">
            <h2 data-translate="blocked_why_headline">Why have I been blocked?</h2>

            <p data-translate="blocked_why_detail">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.</p>
          </div>

          <div class="cf-column">
            <h2 data-translate="blocked_resolve_headline">What can I do to resolve this?</h2>

            <p data-translate="blocked_resolve_detail">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.</p>
          </div>
        </div>
      </div><!-- /.section -->

      <div class="cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300">
    <p class="text-13">
      <span class="cf-footer-item sm:block sm:mb-1">Cloudflare Ray ID: <strong class="font-semibold">95856f506df5ce7f</strong></span>
      <span class="cf-footer-separator sm:hidden">&bull;</span>
      <span id="cf-footer-item-ip" class="cf-footer-item hidden sm:block sm:mb-1">
        Your IP:
        <button type="button" id="cf-footer-ip-reveal" class="cf-footer-ip-reveal-btn">Click to reveal</button>
        <span class="hidden" id="cf-footer-ip">*************</span>
        <span class="cf-footer-separator sm:hidden">&bull;</span>
      </span>
      <span class="cf-footer-item sm:block sm:mb-1"><span>Performance &amp; security by</span> <a rel="noopener noreferrer" href="https://www.cloudflare.com/5xx-error-landing" id="brand_link" target="_blank">Cloudflare</a></span>
      
    </p>
    <script>(function(){function d(){var b=a.getElementById("cf-footer-item-ip"),c=a.getElementById("cf-footer-ip-reveal");b&&"classList"in b&&(b.classList.remove("hidden"),c.addEventListener("click",function(){c.classList.add("hidden");a.getElementById("cf-footer-ip").classList.remove("hidden")}))}var a=document;document.addEventListener&&a.addEventListener("DOMContentLoaded",d)})();</script>
  </div><!-- /.error-footer -->

    </div><!-- /#cf-error-details -->
  </div><!-- /#cf-wrapper -->

  <script>
    window._cf_translation = {};
    
    
  </script>
</body>
</html>
[01-Jul-2025 11:06:32 UTC] AUI Debug - Upload completed for ban-chai_1.png: FAILED
[01-Jul-2025 11:06:32 UTC] AUI Debug - Group upload completed. Total results: 2
[01-Jul-2025 11:06:32 UTC] AUI Debug - Group result #0: ban-chai.png (ID: file_0_1751367986_5413) - FAILED
[01-Jul-2025 11:06:32 UTC] AUI Debug - Group result #1: ban-chai_1.png (ID: file_1_1751367986_5434) - FAILED
[01-Jul-2025 11:06:32 UTC] AUI Debug - Group 0 upload completed, got 2 results
[01-Jul-2025 11:06:32 UTC] AUI Debug - Result for ban-chai.png (ID: file_0_1751367986_5413): FAILED
[01-Jul-2025 11:06:32 UTC] AUI Debug - Result for ban-chai_1.png (ID: file_1_1751367986_5434): FAILED
[01-Jul-2025 11:06:32 UTC] AUI Debug - Added result for ban-chai.png (ID: file_0_1751367986_5413)
[01-Jul-2025 11:06:32 UTC] AUI Debug - DUPLICATE DETECTED! Skipping result for ban-chai.png (ID: file_0_1751367986_5413)
[01-Jul-2025 11:06:32 UTC] AUI Debug - Total results: 1
[01-Jul-2025 11:06:32 UTC] AUI Debug - Successful: 0
[01-Jul-2025 11:06:32 UTC] AUI Debug - Failed: 1
[01-Jul-2025 11:06:32 UTC] AUI Debug - Final results being sent to client:
[01-Jul-2025 11:06:32 UTC] AUI Debug - Final result #0: ban-chai.png (ID: file_0_1751367986_5413)
[01-Jul-2025 11:07:20 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::hasChildren($allow_links = true) should either be compatible with RecursiveDirectoryIterator::hasChildren(bool $allowLinks = false): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 57
[01-Jul-2025 11:07:20 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::rewind() should either be compatible with FilesystemIterator::rewind(): void, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 35
[01-Jul-2025 11:07:20 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::next() should either be compatible with DirectoryIterator::next(): void, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 42
[01-Jul-2025 11:07:20 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Extension_Filter::getChildren() should either be compatible with RecursiveFilterIterator::getChildren(): ?RecursiveFilterIterator, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\filter\class-ai1wm-recursive-extension-filter.php on line 47
[01-Jul-2025 11:07:20 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Extension_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\filter\class-ai1wm-recursive-extension-filter.php on line 37
[01-Jul-2025 11:07:20 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Exclude_Filter::getChildren() should either be compatible with RecursiveFilterIterator::getChildren(): ?RecursiveFilterIterator, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\filter\class-ai1wm-recursive-exclude-filter.php on line 41
[01-Jul-2025 11:07:20 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Exclude_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\filter\class-ai1wm-recursive-exclude-filter.php on line 37
[01-Jul-2025 11:07:20 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\laragon\www\reup\wp-content\plugins\all-in-once-wp-migration\lib\vendor\servmask\filter\class-ai1wm-recursive-newline-filter.php on line 28
