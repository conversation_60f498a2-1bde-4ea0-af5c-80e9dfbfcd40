=== Duplicator - Backups & Migration Plugin - Cloud Backups, Scheduled Backups, & More  ===
Contributors: seedprod, smub, andreamk
Tags: backup, database backup, wordpress backup, cloud backup, migration
Requires at least: 5.3
Tested up to: 6.8
Requires PHP: 7.4
Stable tag: 1.5.12
License: GPLv2

The best WordPress backup and migration plugin. Quickly and easily backup ,migrate, copy, move, or clone your site from one location to another. Simplify backups & migrations without limits.

== Description ==

= WordPress Backup & Migration Plugin =

[Duplicator](https://duplicator.com/?utm_source=wprepo&utm_medium=link&utm_content=top_link&utm_campaign=duplicator_lite) provides a simple way to move WordPress sites, create reliable backups, or clone a site for staging. With Duplicator, you can easily migrate, transfer, or clone your WordPress site between domains or hosts with no downtime. Create full backups of your website, or package your entire site to download and install elsewhere with only a few steps.

At Duplicator, reliabilty, security, and ease of use are our top priorities. Our variety of cloud backup integrations and easy migration wizard make <PERSON><PERSON><PERSON><PERSON> the most beginner-friendly backup and migration plugin on the market. You don't have to hire a developer. Create a backup and migrate sites in just a few minutes.

> <strong>Duplicator Pro</strong><br />
> This plugin is the Lite version of Duplicator Pro, which comes with scheduled backups, cloud storage integrations, multisite support, and more. [Get Duplicator Pro for the complete migration and backup solution](https://duplicator.com/?utm_source=wprepo&utm_medium=link&utm_campaign=duplicator_lite&utm_content=get_duplicator_pro).

https://www.youtube.com/watch?v=MSa83NkLDmU


= Easy Site Migration, Backup, and Cloning =

Duplicator streamlines site migrations by packaging your website files and database into a single file, known as a "backup". Download and re-install your "backup" on any new WordPress location or server without dealing with complicated setups. Launch at your new destination without installing WordPress. Duplicator is the **only** migration and backup plugin that works on an empty site.


See why experts love Duplicator:

> "Duplicator provides an easy to use tool to make backups of your site, or to transfer it to another location." 
> Richard McAdams - Expert Web Developer


= Secure WordPress Backups = 

Duplicator offers cloud [WordPress backups](https://duplicator.com/secure-wordpress-backups/?utm_source=wprepo&utm_medium=link&utm_content=secure_wordpress_backups&utm_campaign=duplicator_lite) with military-grade encryption. Automatically backup your entire WordPress site to secure cloud storage.  

= Recovery Points (1-click Restore) =

Duplicator makes [1-click restores](https://duplicator.com/disaster-recovery-1-click-restore/?utm_source=wprepo&utm_medium=link&utm_content=1_click_restores&utm_campaign=duplicator_lite) for WordPress backups easy and stress-free. Quickly restore your entire website in minutes just like a time machine.

= Fast WordPress Migrations =

Duplicator makes [WordPress website migrations](https://duplicator.com/wordpress-migration/?utm_source=wprepo&utm_medium=link&utm_content=wordpress_migrations&utm_campaign=duplicator_lite) fast and stress-free. Quickly move to a new host, domain, or server. No downtime, no data loss, and no coding required.

= WordPress Multisite Backups =

Duplicator offers automatic [WordPress Multisite backups](https://duplicator.com/wordpress-multisite-backups/?utm_source=wprepo&utm_medium=link&utm_content=wordpress_multisite_backups&utm_campaign=duplicator_lite) with easy 1-click restore. Safely backup your entire Multisite network to secure cloud storage.

= WooCommerce Backups =

Duplicator offers reliable [WooCommerce backups](https://duplicator.com/woocommerce-backups/?utm_source=wprepo&utm_medium=link&utm_content=woocommerce_backups&utm_campaign=duplicator_lite) with military-grade encryption. Easily and automatically back up your entire online store to secure cloud storage.

= Pre-configured WordPress Installs =

Never start from scratch with Duplicator’s smart [pre-configured WordPress installs](https://duplicator.com/pre-configured-wordpress-installations/?utm_source=wprepo&utm_medium=link&utm_content=pre_configured_installs&utm_campaign=duplicator_lite). Save time and hassle duplicating ready-made sites with 1-click.

= WordPress Recovery Points with Quick Rollbacks =

Duplicator offers hourly recovery points and [1-click rollbacks for WordPress sites](https://duplicator.com/wordpress-recovery-points-rollback/?utm_source=wprepo&utm_medium=link&utm_content=wordpress_recovery_points&utm_campaign=duplicator_lite). Quickly and automatically recover from failed WordPress updates or disasters.

= Partial WordPress Backup Plugin =

Duplicator makes [partial backups for WordPress](https://duplicator.com/partial-wordpress-backup-plugin/?utm_source=wprepo&utm_medium=link&utm_content=partial_backups&utm_campaign=duplicator_lite) quick and easy. Save storage and restore sites faster with database-only, media-only, or completely custom backups.


= Server to Server WordPress Migration Import Tool =

Duplicator makes [server-to-server WordPress migrations](https://duplicator.com/server-to-server-wordpress-migration/?utm_source=wprepo&utm_medium=link&utm_content=server_to_server_migrations&utm_campaign=duplicator_lite) fast and hassle-free. Quickly import your website to a new server in minutes. No downtime, no data loss.


= Smart WordPress Migration Wizard =

Duplicator’s smart [WordPress migration wizard](https://duplicator.com/wordpress-migration-wizard/?utm_source=wprepo&utm_medium=link&utm_content=wordpress_migration_wizard&utm_campaign=duplicator_lite) makes transferring your website to a new host or server effortless. No downtime, no data loss, and no code required.


= Drag & Drop Import WordPress Website Tool =

Migrating WordPress sites has never been easier with Duplicator’s [drag & drop import tools](https://duplicator.com/drag-drop-import-wordpress-tool/?utm_source=wprepo&utm_medium=link&utm_content=wordpress_migration_wizard&utm_campaign=duplicator_lite). Quickly transfer your site to a new host or server in minutes, no code required.


= Clone WordPress Website Plugin =

Duplicator [clones your entire WordPress website](https://duplicator.com/clone-wordpress-plugin/?utm_source=wprepo&utm_medium=link&utm_content=clone_wordpress_website&utm_campaign=duplicator_lite) with 1-click, no code needed. Perfect for staging sites, sandbox, or site migration.


= Duplicator Pro Features =
Duplicator Pro takes Duplicator to the next level with features you'll love, such as:

* Drag and Drop installs - just drag the backup file to the destination site!
* Scheduled backups
* Cloud Storage to Dropbox Backups, Google Drive Backups, Microsoft OneDrive Backups, Amazon S3 Backups and FTP/SFTP Backups
* Custom Backups and Cloning: want just plugins, or just themes, just the database? No problem!
* A special 2-step streamlined installer mode for mega-fast installs
* Recovery Points added for very fast emergency site restores
* Support for managed and shared hosts such as WordPress.com, WPEngine, GoDaddy Managed, and more
* Multi-threaded to support larger websites &amp; databases
* Migrate an entire multisite WordPress network or a sub site as a standalone site
* Database and user creation *in the installer* with cPanel API
* Connect to cPanel directly from the installer
* Custom plugin hooks for developers
* Advanced permissions
* Email notifications
* Professional support
* ... and much more!

= Supported Backup Cloud Storage Integrations =
We support any Amazon S3 compatible storage providers plus these first-party integrations.

* [Localhost Backups](https://duplicator.com/secure-wordpress-backups/?utm_source=wprepo&utm_medium=link&utm_content=localhost_backups&utm_campaign=duplicator_lite)
* [FTP/ SFTP Backups](https://duplicator.com/ftp-wordpress-backup-plugin/?utm_source=wprepo&utm_medium=link&utm_content=ftp_sftp_backups&utm_campaign=duplicator_lite)
* [Dropbox Backups](https://duplicator.com/dropbox-wordpress-backup-plugin/?utm_source=wprepo&utm_medium=link&utm_content=dropbox_backups&utm_campaign=duplicator_lite)
* [Google Drive Backups](https://duplicator.com/google-drive-wordpress-backup-plugin/?utm_source=wprepo&utm_medium=link&utm_content=google_drive_backups&utm_campaign=duplicator_lite)
* [Microsoft OneDrive Backups](https://duplicator.com/microsoft-one-drive-wordpress-backup-plugin/?utm_source=wprepo&utm_medium=link&utm_content=microsoft_one_drive_backups&utm_campaign=duplicator_lite)
* [Amazon S3 Backups](https://duplicator.com/amazon-s3-wordpress-backup-plugin/?utm_source=wprepo&utm_medium=link&utm_content=amazon_s3_backups&utm_campaign=duplicator_lite)
* [Cloudflare R2 Backups](https://duplicator.com/cloudflare-r2-wordpress-backup-plugin/?utm_source=wprepo&utm_medium=link&utm_content=cloudflare_r2_backups&utm_campaign=duplicator_lite)
* [Wasabi Backups](https://duplicator.com/wasabi-wordpress-backup-plugin/?utm_source=wprepo&utm_medium=link&utm_content=wasabi_backups&utm_campaign=duplicator_lite)
* [Dream Objects Backups](https://duplicator.com/dream-objects-wordpress-backup-plugin/?utm_source=wprepo&utm_medium=link&utm_content=dream_objects_backups&utm_campaign=duplicator_lite)
* [Vultr Backups](https://duplicator.com/vultr-wordpress-backup-plugin/?utm_source=wprepo&utm_medium=link&utm_content=vultr_backups&utm_campaign=duplicator_lite)
* [Digital Ocean Spaces Backups](https://duplicator.com/wordpress-backups-for-digitalocean-spaces/?utm_source=wprepo&utm_medium=link&utm_content=digital_ocean_spaces_backups&utm_campaign=duplicator_lite)
* [Google Cloud Storage Backups](https://duplicator.com/google-cloud-storage-wordpress-backup-plugin/?utm_source=wprepo&utm_medium=link&utm_content=google_cloud_storage_backups&utm_campaign=duplicator_lite)
* [Backblaze B2 Storage Backups](https://duplicator.com/backblaze-b2-wordpress-backup-plugin/?utm_source=wprepo&utm_medium=link&utm_content=backblaze_b2_storage_backups&utm_campaign=duplicator_lite)
* [Linode Object Storage Backups](https://duplicator.com/linode-object-storage-wordpress-backup-plugin/?utm_source=wprepo&utm_medium=link&utm_content=linode_object_storage_backups&utm_campaign=duplicator_lite)


You can easily see why Duplicator is the best WordPress backup and migration plugin on the market! Want to unlock these features? [Upgrade to our Pro version](https://duplicator.com/?utm_source=wprepo&utm_medium=link&utm_content=upgrade_to_pro&utm_campaign=duplicator_lite) 

= Branding Guidelines =

Duplicator&reg; is a registered trademark of Snap Creek LLC. When writing about the backup & migration plugin by Duplicator, please make sure to uppercase the initial first letter.

* Duplicator (correct)
* duplicator (incorrect)


== Screenshots ==

1. Main Interface for all Backups
2. Create Backup Step 1
3. Create Backup Step 2
4. Build Process
5. Installer Screen

== Frequently Asked Questions ==

= Who Should Use Duplicator? =

Duplicator is perfect for business owners, bloggers, designers, developers, photographers, and basically everyone else. If you want to create a WordPress Backup or Migration, then you need to use Duplicator.

= What's required to use Duplicator? =

Duplicator is a WordPress Plugin. In order to use Duplicator, you must have a self-hosted WordPress site. That's all.

= Do I need coding skills to use Duplicator? = 

Absolutely not. You can create backups and migrations without any coding knowledge. Duplicator is the most beginner-friendly backup solution in the market.

= Will Duplicator slow down my website? =

Absolutely not. Duplicator is carefully built with performance in mind. We have developed everything with best practices and modern standards to ensure things run smooth and fast.

= Can I backup WooCommerce sites? =

Yes. Duplicator makes [backing up WooCommerce sites](https://duplicator.com/woocommerce-backups/?utm_source=wprepo&utm_medium=link&utm_content=faq_woocommerce_backups&utm_campaign=duplicator_lite) easy and stress-free. 


= Is this plugin compatible with WordPress multisite (MU)? =
Yes, however you will need [Duplicator Pro](https://duplicator.com/wordpress-multisite-backups/?utm_source=wprepo&utm_medium=link&utm_content=faq_dpro_multisiteinfo&utm_campaign=duplicator_lite) for full multisite network migrations &backups and the ability to install a multisite subsite as a standalone site.

= Where can I get more help and support for this plugin? =
Purchase a Pro License for quick support [Duplicator Pro](https://duplicator.com/pricing/?utm_source=wprepo&utm_medium=link&utm_content=faq_support&utm_campaign=duplicator_lite)

= Does Duplicator have a knowledge base or FAQ? =
Yes. Please see [all documents](https://duplicator.com/knowledge-base/?utm_source=wprepo&utm_medium=link&utm_content=faq_docs&utm_campaign=duplicator_lite) at duplicator.com

= Installation Instructions =
1. Upload `duplicator` folder to the `/wp-content/plugins/` directory
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Click on the Duplicator link from the main menu
4. Check out the help by clicking the help icon and create your first backup.

The Duplicator requires php 5.3 or higher.

== Changelog ==

Please see the following url:
[https://duplicator.com/knowledge-base/changelog/](https://duplicator.com/knowledge-base/changelog/?lite&utm_source=wprepo&utm_medium=link&utm_content=changelog_support&utm_campaign=duplicator_lite)



== Upgrade Notice ==
