<?php
/**
 * Config Manager Class
 * Quản lý cấu hình các domain API và settings
 */

if (!defined('ABSPATH')) {
    exit;
}

class AUI_Config_Manager {
    
    private $domains_option = 'aui_api_domains';
    private $settings_option = 'aui_settings';
    
    /**
     * Constructor
     */
    public function __construct() {
        // Initialize default values if not exist
        $this->init_defaults();
    }
    
    /**
     * Initialize default values
     */
    private function init_defaults() {
        if (!get_option($this->domains_option)) {
            $default_domains = array(
                'https://cn-av.com',
                'https://hentai-sub.com'
            );
            update_option($this->domains_option, $default_domains);
        }
        
        if (!get_option($this->settings_option)) {
            $default_settings = array(
                'retry_attempts' => 2,
                'delay_seconds' => 1,
                'auto_delete' => 1
            );
            update_option($this->settings_option, $default_settings);
        }
    }
    
    /**
     * Get all API domains
     * @return array
     */
    public function get_domains() {
        $domains = get_option($this->domains_option, array());
        return is_array($domains) ? $domains : array();
    }
    
    /**
     * Add new domain
     * @param string $domain
     * @return bool
     */
    public function add_domain($domain) {
        $domain = esc_url_raw($domain);
        if (empty($domain)) {
            return false;
        }
        
        $domains = $this->get_domains();
        if (!in_array($domain, $domains)) {
            $domains[] = $domain;
            return update_option($this->domains_option, $domains);
        }
        
        return false; // Domain already exists
    }
    
    /**
     * Remove domain
     * @param string $domain
     * @return bool
     */
    public function remove_domain($domain) {
        $domains = $this->get_domains();
        $key = array_search($domain, $domains);
        
        if ($key !== false) {
            unset($domains[$key]);
            $domains = array_values($domains); // Re-index array
            return update_option($this->domains_option, $domains);
        }
        
        return false;
    }
    
    /**
     * Update domains list
     * @param array $domains
     * @return bool
     */
    public function update_domains($domains) {
        if (!is_array($domains)) {
            return false;
        }
        
        // Sanitize domains
        $clean_domains = array();
        foreach ($domains as $domain) {
            $clean_domain = esc_url_raw($domain);
            if (!empty($clean_domain)) {
                $clean_domains[] = $clean_domain;
            }
        }
        
        return update_option($this->domains_option, $clean_domains);
    }
    
    /**
     * Get settings
     * @return array
     */
    public function get_settings() {
        $settings = get_option($this->settings_option, array());
        
        // Ensure default values
        $defaults = array(
            'retry_attempts' => 2,
            'delay_seconds' => 1,
            'auto_delete' => 1
        );
        
        return wp_parse_args($settings, $defaults);
    }
    
    /**
     * Update settings
     * @param array $settings
     * @return bool
     */
    public function update_settings($settings) {
        if (!is_array($settings)) {
            return false;
        }
        
        // Sanitize settings
        $clean_settings = array();
        
        if (isset($settings['retry_attempts'])) {
            $clean_settings['retry_attempts'] = max(0, intval($settings['retry_attempts']));
        }
        
        if (isset($settings['delay_seconds'])) {
            $clean_settings['delay_seconds'] = max(0, intval($settings['delay_seconds']));
        }
        
        if (isset($settings['auto_delete'])) {
            $clean_settings['auto_delete'] = $settings['auto_delete'] ? 1 : 0;
        }
        
        $current_settings = $this->get_settings();
        $updated_settings = wp_parse_args($clean_settings, $current_settings);
        
        return update_option($this->settings_option, $updated_settings);
    }
    
    /**
     * Get specific setting
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public function get_setting($key, $default = null) {
        $settings = $this->get_settings();
        return isset($settings[$key]) ? $settings[$key] : $default;
    }
    
    /**
     * Get domain count
     * @return int
     */
    public function get_domain_count() {
        return count($this->get_domains());
    }
    
    /**
     * Check if domains are configured
     * @return bool
     */
    public function has_domains() {
        return $this->get_domain_count() > 0;
    }
    
    /**
     * Get next domain for round-robin distribution
     * @param int $index
     * @return string|false
     */
    public function get_domain_by_index($index) {
        $domains = $this->get_domains();
        if (empty($domains)) {
            return false;
        }
        
        $domain_index = $index % count($domains);
        return $domains[$domain_index];
    }
    
    /**
     * Validate domain URL
     * @param string $domain
     * @return bool
     */
    public function validate_domain($domain) {
        return filter_var($domain, FILTER_VALIDATE_URL) !== false;
    }
    
    /**
     * Reset to default configuration
     * @return bool
     */
    public function reset_to_defaults() {
        $default_domains = array(
            'https://cn-av.com',
            'https://hentai-sub.com'
        );
        
        $default_settings = array(
            'retry_attempts' => 2,
            'delay_seconds' => 1,
            'auto_delete' => 1
        );
        
        $domains_updated = update_option($this->domains_option, $default_domains);
        $settings_updated = update_option($this->settings_option, $default_settings);
        
        return $domains_updated && $settings_updated;
    }
}
