<?php
/**
 * Admin Page Class
 * Tạo trang quản trị cho plugin
 */

if (!defined('ABSPATH')) {
    exit;
}

class AUI_Admin_Page {
    
    private $config_manager;
    private $image_uploader;
    
    /**
     * Constructor
     * @param AUI_Config_Manager $config_manager
     * @param AUI_Image_Uploader $image_uploader
     */
    public function __construct($config_manager, $image_uploader) {
        $this->config_manager = $config_manager;
        $this->image_uploader = $image_uploader;
        
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'handle_form_submissions'));
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_menu_page(
            'Auto Upload Image',
            'Auto Upload Image',
            'manage_options',
            'auto-upload-image',
            array($this, 'render_main_page'),
            'dashicons-upload',
            30
        );
        
        add_submenu_page(
            'auto-upload-image',
            'Cấu hình API',
            'Cấu hình API',
            'manage_options',
            'auto-upload-image-config',
            array($this, 'render_config_page')
        );
    }
    
    /**
     * Handle form submissions
     */
    public function handle_form_submissions() {
        if (!current_user_can('manage_options')) {
            return;
        }
        
        // Handle domain configuration
        if (isset($_POST['aui_save_domains']) && wp_verify_nonce($_POST['aui_nonce'], 'aui_save_domains')) {
            $domains = isset($_POST['domains']) ? $_POST['domains'] : array();
            $domains = array_filter(array_map('trim', $domains));
            
            if ($this->config_manager->update_domains($domains)) {
                add_action('admin_notices', function() {
                    echo '<div class="notice notice-success"><p>Cấu hình domain đã được lưu!</p></div>';
                });
            } else {
                add_action('admin_notices', function() {
                    echo '<div class="notice notice-error"><p>Có lỗi khi lưu cấu hình domain!</p></div>';
                });
            }
        }
        
        // Handle settings
        if (isset($_POST['aui_save_settings']) && wp_verify_nonce($_POST['aui_nonce'], 'aui_save_settings')) {
            $settings = array(
                'retry_attempts' => intval($_POST['retry_attempts']),
                'delay_seconds' => intval($_POST['delay_seconds']),
                'auto_delete' => isset($_POST['auto_delete']) ? 1 : 0
            );
            
            if ($this->config_manager->update_settings($settings)) {
                add_action('admin_notices', function() {
                    echo '<div class="notice notice-success"><p>Cài đặt đã được lưu!</p></div>';
                });
            } else {
                add_action('admin_notices', function() {
                    echo '<div class="notice notice-error"><p>Có lỗi khi lưu cài đặt!</p></div>';
                });
            }
        }
    }
    
    /**
     * Render main page
     */
    public function render_main_page() {
        $domains = $this->config_manager->get_domains();
        $settings = $this->config_manager->get_settings();
        ?>
        <div class="wrap">
            <h1>Auto Upload Image</h1>
            
            <?php if (empty($domains)): ?>
                <div class="notice notice-warning">
                    <p>Chưa có domain API nào được cấu hình. <a href="<?php echo admin_url('admin.php?page=auto-upload-image-config'); ?>">Cấu hình ngay</a></p>
                </div>
            <?php endif; ?>
            
            <div class="aui-upload-section">
                <h2>Upload Ảnh</h2>
                <p>Nhập URL ảnh (mỗi URL một dòng):</p>
                
                <form id="aui-upload-form">
                    <textarea id="aui-image-urls" rows="10" cols="80" placeholder="https://example.com/image1.jpg&#10;https://example.com/image2.jpg&#10;https://example.com/image3.jpg"></textarea>
                    <br><br>
                    <button type="submit" class="button button-primary">Upload Ảnh</button>
                    <span id="aui-upload-status"></span>
                </form>
                
                <div id="aui-upload-results" style="display: none;">
                    <h3>Kết quả Upload</h3>
                    <div id="aui-results-content"></div>
                    <br>
                    <button id="aui-delete-files" class="button button-secondary" style="display: none;">Xóa file đã upload thành công</button>
                </div>
            </div>
            
            <div class="aui-info-section">
                <h3>Thông tin cấu hình hiện tại</h3>
                <table class="widefat">
                    <tr>
                        <td><strong>Số domain API:</strong></td>
                        <td><?php echo count($domains); ?></td>
                    </tr>
                    <tr>
                        <td><strong>Domains:</strong></td>
                        <td>
                            <?php if (!empty($domains)): ?>
                                <ul>
                                    <?php foreach ($domains as $domain): ?>
                                        <li><?php echo esc_html($domain); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            <?php else: ?>
                                <em>Chưa cấu hình</em>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Số lần thử lại:</strong></td>
                        <td><?php echo $settings['retry_attempts']; ?></td>
                    </tr>
                    <tr>
                        <td><strong>Delay giữa upload:</strong></td>
                        <td><?php echo $settings['delay_seconds']; ?> giây</td>
                    </tr>
                    <tr>
                        <td><strong>Tự động xóa file:</strong></td>
                        <td><?php echo $settings['auto_delete'] ? 'Có' : 'Không'; ?></td>
                    </tr>
                </table>
            </div>
        </div>
        
        <style>
        .aui-upload-section {
            background: #fff;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid #ccd0d4;
            box-shadow: 0 1px 1px rgba(0,0,0,.04);
        }
        .aui-info-section {
            background: #fff;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid #ccd0d4;
            box-shadow: 0 1px 1px rgba(0,0,0,.04);
        }
        #aui-image-urls {
            width: 100%;
            max-width: 800px;
        }
        .aui-result-item {
            padding: 10px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .aui-result-success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .aui-result-error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .aui-result-links {
            margin-top: 10px;
        }
        .aui-result-links a {
            margin-right: 10px;
            text-decoration: none;
        }
        #aui-upload-status {
            margin-left: 10px;
            font-weight: bold;
        }
        .aui-loading {
            color: #0073aa;
        }
        .aui-success {
            color: #46b450;
        }
        .aui-error {
            color: #dc3232;
        }
        </style>
        <?php
    }
    
    /**
     * Render config page
     */
    public function render_config_page() {
        $domains = $this->config_manager->get_domains();
        $settings = $this->config_manager->get_settings();
        ?>
        <div class="wrap">
            <h1>Cấu hình Auto Upload Image</h1>
            
            <form method="post">
                <?php wp_nonce_field('aui_save_domains', 'aui_nonce'); ?>
                
                <h2>Cấu hình Domain API</h2>
                <table class="form-table">
                    <tr>
                        <th scope="row">Domains API</th>
                        <td>
                            <div id="aui-domains-list">
                                <?php if (!empty($domains)): ?>
                                    <?php foreach ($domains as $index => $domain): ?>
                                        <div class="aui-domain-item">
                                            <input type="url" name="domains[]" value="<?php echo esc_attr($domain); ?>" style="width: 400px;" />
                                            <button type="button" class="button aui-remove-domain">Xóa</button>
                                        </div>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <div class="aui-domain-item">
                                        <input type="url" name="domains[]" value="" placeholder="https://example.com" style="width: 400px;" />
                                        <button type="button" class="button aui-remove-domain">Xóa</button>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <br>
                            <button type="button" id="aui-add-domain" class="button">Thêm Domain</button>
                            <p class="description">Nhập URL đầy đủ của các domain API (ví dụ: https://cn-av.com)</p>
                        </td>
                    </tr>
                </table>
                
                <p class="submit">
                    <input type="submit" name="aui_save_domains" class="button-primary" value="Lưu Domain" />
                </p>
            </form>
            
            <hr>
            
            <form method="post">
                <?php wp_nonce_field('aui_save_settings', 'aui_nonce'); ?>
                
                <h2>Cài đặt Upload</h2>
                <table class="form-table">
                    <tr>
                        <th scope="row">Số lần thử lại</th>
                        <td>
                            <input type="number" name="retry_attempts" value="<?php echo esc_attr($settings['retry_attempts']); ?>" min="0" max="10" />
                            <p class="description">Số lần thử lại khi upload thất bại (0-10)</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Delay giữa upload</th>
                        <td>
                            <input type="number" name="delay_seconds" value="<?php echo esc_attr($settings['delay_seconds']); ?>" min="0" max="60" />
                            <p class="description">Số giây delay giữa các upload cùng domain (0-60)</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Tự động xóa file</th>
                        <td>
                            <label>
                                <input type="checkbox" name="auto_delete" value="1" <?php checked($settings['auto_delete'], 1); ?> />
                                Tự động xóa file local sau khi upload thành công
                            </label>
                        </td>
                    </tr>
                </table>
                
                <p class="submit">
                    <input type="submit" name="aui_save_settings" class="button-primary" value="Lưu Cài đặt" />
                </p>
            </form>
        </div>
        
        <script>
        jQuery(document).ready(function($) {
            // Add domain
            $('#aui-add-domain').click(function() {
                var newDomain = '<div class="aui-domain-item">' +
                    '<input type="url" name="domains[]" value="" placeholder="https://example.com" style="width: 400px;" />' +
                    '<button type="button" class="button aui-remove-domain">Xóa</button>' +
                    '</div>';
                $('#aui-domains-list').append(newDomain);
            });
            
            // Remove domain
            $(document).on('click', '.aui-remove-domain', function() {
                if ($('.aui-domain-item').length > 1) {
                    $(this).parent().remove();
                }
            });
        });
        </script>
        
        <style>
        .aui-domain-item {
            margin-bottom: 10px;
        }
        .aui-domain-item input {
            margin-right: 10px;
        }
        </style>
        <?php
    }
}
