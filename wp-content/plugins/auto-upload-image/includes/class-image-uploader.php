<?php
/**
 * Image Uploader Class
 * Xử lý upload ảnh lên các domain API
 */

if (!defined('ABSPATH')) {
    exit;
}

class AUI_Image_Uploader {
    
    private $config_manager;
    private $upload_results = array();
    private $current_domain_index = 0;
    
    /**
     * Constructor
     * @param AUI_Config_Manager $config_manager
     */
    public function __construct($config_manager) {
        $this->config_manager = $config_manager;
        
        // Add AJAX handlers
        add_action('wp_ajax_aui_upload_images', array($this, 'ajax_upload_images'));
        add_action('wp_ajax_aui_delete_uploaded_files', array($this, 'ajax_delete_uploaded_files'));
    }
    
    /**
     * AJAX handler for uploading images
     */
    public function ajax_upload_images() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'aui_nonce')) {
            wp_die('Security check failed');
        }

        // Check user capabilities
        if (!current_user_can('upload_files')) {
            wp_die('Insufficient permissions');
        }

        // Check if files were uploaded
        if (empty($_FILES['image_files']['name'][0])) {
            wp_send_json_error('No files uploaded');
        }

        $results = $this->process_uploaded_files($_FILES['image_files']);

        wp_send_json_success($results);
    }
    
    /**
     * AJAX handler for deleting uploaded files
     */
    public function ajax_delete_uploaded_files() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'aui_nonce')) {
            wp_die('Security check failed');
        }

        // Check user capabilities
        if (!current_user_can('upload_files')) {
            wp_die('Insufficient permissions');
        }

        $file_paths = isset($_POST['file_paths']) ? $_POST['file_paths'] : array();

        if (empty($file_paths)) {
            wp_send_json_error('No file paths provided');
        }

        $deleted_count = 0;
        $upload_dir = wp_upload_dir();
        $temp_dir = $upload_dir['basedir'] . '/aui-temp/';

        foreach ($file_paths as $file_path) {
            // Security check: only allow deletion from temp directory
            if (strpos($file_path, $temp_dir) === 0 && file_exists($file_path)) {
                if (unlink($file_path)) {
                    $deleted_count++;
                }
            }
        }

        // Clean up empty temp directory
        if (is_dir($temp_dir) && count(scandir($temp_dir)) == 2) { // Only . and ..
            rmdir($temp_dir);
        }

        wp_send_json_success(array(
            'deleted_count' => $deleted_count,
            'total_files' => count($file_paths)
        ));
    }
    
    /**
     * Process uploaded files
     * @param array $files
     * @return array
     */
    public function process_uploaded_files($files) {
        $upload_dir = wp_upload_dir();
        $temp_dir = $upload_dir['basedir'] . '/aui-temp/';

        // Create temp directory if not exists
        if (!file_exists($temp_dir)) {
            wp_mkdir_p($temp_dir);
        }

        $uploaded_files = array();
        $file_count = count($files['name']);

        // Process each uploaded file
        for ($i = 0; $i < $file_count; $i++) {
            if ($files['error'][$i] !== UPLOAD_ERR_OK) {
                continue;
            }

            $filename = sanitize_file_name($files['name'][$i]);
            $temp_file = $files['tmp_name'][$i];

            // Validate file type
            $file_type = wp_check_filetype($filename);
            if (!in_array($file_type['type'], array('image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'))) {
                continue;
            }

            // Move to temp directory
            $temp_path = $temp_dir . $filename;
            if (move_uploaded_file($temp_file, $temp_path)) {
                $uploaded_files[] = array(
                    'filename' => $filename,
                    'path' => $temp_path,
                    'url' => $upload_dir['baseurl'] . '/aui-temp/' . $filename,
                    'size' => filesize($temp_path)
                );
            }
        }

        if (empty($uploaded_files)) {
            return array(
                'success' => false,
                'message' => 'No valid image files uploaded',
                'results' => array()
            );
        }

        // Upload to APIs
        $results = $this->upload_multiple_images($uploaded_files);

        return $results;
    }

    /**
     * Upload multiple images
     * @param array $image_files
     * @return array
     */
    public function upload_multiple_images($image_files) {
        $this->upload_results = array();
        $domains = $this->config_manager->get_domains();
        
        if (empty($domains)) {
            return array(
                'success' => false,
                'message' => 'No API domains configured',
                'results' => array()
            );
        }
        
        // Group images by pattern
        $grouped_images = $this->group_images_by_pattern($image_files);

        // Debug log grouping
        error_log('AUI Debug - Grouped images:');
        foreach ($grouped_images as $index => $group) {
            $filenames = array_map(function($file) { return $file['filename']; }, $group);
            error_log('Group ' . $index . ': ' . implode(', ', $filenames));
        }
        
        $all_results = array();
        $successful_uploads = array();
        $failed_uploads = array();
        
        foreach ($grouped_images as $group_index => $group) {
            $group_results = $this->upload_image_group($group, $domains);

            // Add group info to results for better display
            foreach ($group_results as &$result) {
                $result['group_index'] = $group_index;
                $result['group_size'] = count($group);
            }

            $all_results = array_merge($all_results, $group_results);

            foreach ($group_results as $result) {
                if ($result['success']) {
                    $successful_uploads[] = $result;
                } else {
                    $failed_uploads[] = $result;
                }
            }
        }

        // Sort results by group for better display
        usort($all_results, function($a, $b) {
            if ($a['group_index'] !== $b['group_index']) {
                return $a['group_index'] - $b['group_index'];
            }
            // Within same group, sort by filename
            return strcmp($a['filename'], $b['filename']);
        });
        
        return array(
            'success' => true,
            'message' => sprintf('Upload completed. Success: %d, Failed: %d', 
                count($successful_uploads), count($failed_uploads)),
            'results' => $all_results,
            'successful_uploads' => $successful_uploads,
            'failed_uploads' => $failed_uploads,
            'summary' => array(
                'total' => count($all_results),
                'successful' => count($successful_uploads),
                'failed' => count($failed_uploads)
            )
        );
    }
    
    /**
     * Group images by pattern (base name with or without _number)
     * @param array $image_files
     * @return array
     */
    private function group_images_by_pattern($image_files) {
        $groups = array();
        $pattern_groups = array();

        foreach ($image_files as $file) {
            $filename = $file['filename'];
            $base_name = $this->extract_base_name($filename);

            // Group by base name
            if (!isset($pattern_groups[$base_name])) {
                $pattern_groups[$base_name] = array();
            }

            // Extract number for sorting (0 if no number)
            $number = $this->extract_number_from_filename($filename);

            $pattern_groups[$base_name][] = array(
                'file' => $file,
                'number' => $number,
                'filename' => $filename
            );
        }

        // Sort each group by number and add to main groups
        foreach ($pattern_groups as $base_name => $images) {
            // Sort by number (files without number come first)
            usort($images, function($a, $b) {
                return $a['number'] - $b['number'];
            });

            // Extract files
            $sorted_files = array_map(function($item) {
                return $item['file'];
            }, $images);

            $groups[] = $sorted_files;
        }

        return $groups;
    }

    /**
     * Extract base name from filename
     * ban-chai.png -> ban-chai
     * ban-chai_1.png -> ban-chai
     * ban-chai_3.png -> ban-chai
     */
    private function extract_base_name($filename) {
        // Remove extension first
        $name_without_ext = pathinfo($filename, PATHINFO_FILENAME);

        // Check if it has _number pattern
        if (preg_match('/^(.+)_(\d+)$/', $name_without_ext, $matches)) {
            return $matches[1]; // Return base name without _number
        }

        return $name_without_ext; // Return as is if no _number pattern
    }

    /**
     * Extract number from filename for sorting
     * ban-chai.png -> 0
     * ban-chai_1.png -> 1
     * ban-chai_3.png -> 3
     */
    private function extract_number_from_filename($filename) {
        $name_without_ext = pathinfo($filename, PATHINFO_FILENAME);

        if (preg_match('/^.+_(\d+)$/', $name_without_ext, $matches)) {
            return intval($matches[1]);
        }

        return 0; // Files without number get 0 (will be sorted first)
    }
    
    /**
     * Upload a group of images to the same domain
     * @param array $image_group
     * @param array $domains
     * @return array
     */
    private function upload_image_group($image_group, $domains) {
        $domain = $this->get_next_domain($domains);
        $results = array();
        $delay_seconds = $this->config_manager->get_setting('delay_seconds', 1);

        foreach ($image_group as $index => $file) {
            // Add delay between uploads to same domain (except first image)
            if ($index > 0 && count($image_group) > 1) {
                sleep($delay_seconds);
            }

            $result = $this->upload_single_image($file, $domain);
            $results[] = $result;
        }

        return $results;
    }
    
    /**
     * Get next domain using round-robin
     * @param array $domains
     * @return string
     */
    private function get_next_domain($domains) {
        $domain = $domains[$this->current_domain_index % count($domains)];
        $this->current_domain_index++;
        return $domain;
    }
    
    /**
     * Upload single image with retry logic
     * @param array $file
     * @param string $domain
     * @return array
     */
    private function upload_single_image($file, $domain) {
        $retry_attempts = $this->config_manager->get_setting('retry_attempts', 2);
        $filename = $file['filename'];
        $file_path = $file['path'];
        $file_url = $file['url'];

        // Extract file info
        $file_info = pathinfo($filename);
        $name_without_ext = $file_info['filename'];
        $extension = isset($file_info['extension']) ? $file_info['extension'] : 'jpg';

        // Prepare API data
        $api_data = array(
            'upload_auto_delete' => 0,
            'type' => 'image/' . $extension,
            'nameFile' => $name_without_ext,
            'extFile' => $extension,
            'linkImage' => $file_url,
            'size' => $file['size']
        );

        $last_error = '';

        // Try upload with retries
        for ($attempt = 0; $attempt <= $retry_attempts; $attempt++) {
            $result = $this->make_api_request($domain, $api_data);

            if ($result['success']) {
                $upload_result = array(
                    'success' => true,
                    'original_path' => $file_path,
                    'original_url' => $file_url,
                    'filename' => $filename,
                    'domain' => $domain,
                    'attempt' => $attempt + 1,
                    'response' => $result['data']
                );

                // Delete temp file if auto_delete is enabled
                if ($this->config_manager->get_setting('auto_delete', 1)) {
                    if (file_exists($file_path)) {
                        unlink($file_path);
                        $upload_result['temp_file_deleted'] = true;
                    }
                }

                return $upload_result;
            }

            $last_error = $result['error'];

            // Don't sleep after last attempt
            if ($attempt < $retry_attempts) {
                sleep(1); // Wait 1 second before retry
            }
        }

        return array(
            'success' => false,
            'original_path' => $file_path,
            'original_url' => $file_url,
            'filename' => $filename,
            'domain' => $domain,
            'attempts' => $retry_attempts + 1,
            'error' => $last_error
        );
    }
    
    /**
     * Make API request to upload image
     * @param string $domain
     * @param array $data
     * @return array
     */
    private function make_api_request($domain, $data) {
        $api_url = rtrim($domain, '/') . '/upload/api/upload-image';
        
        $response = wp_remote_post($api_url, array(
            'headers' => array(
                'Content-Type' => 'application/json',
            ),
            'body' => json_encode($data),
            'timeout' => 30,
            'sslverify' => false
        ));
        
        if (is_wp_error($response)) {
            return array(
                'success' => false,
                'error' => $response->get_error_message()
            );
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);
        
        if ($response_code !== 200) {
            return array(
                'success' => false,
                'error' => sprintf('HTTP %d: %s', $response_code, $response_body)
            );
        }
        
        $decoded_response = json_decode($response_body, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            return array(
                'success' => false,
                'error' => 'Invalid JSON response: ' . $response_body
            );
        }
        
        if (!isset($decoded_response['type']) || $decoded_response['type'] !== 'success') {
            $error_msg = isset($decoded_response['message']) ? $decoded_response['message'] : 'Unknown API error';
            return array(
                'success' => false,
                'error' => $error_msg
            );
        }
        
        return array(
            'success' => true,
            'data' => $decoded_response
        );
    }
}
