<?php
/**
 * Image Uploader Class
 * Xử lý upload ảnh lên các domain API
 */

if (!defined('ABSPATH')) {
    exit;
}

class AUI_Image_Uploader {

    private $config_manager;
    private $upload_results = array();
    private $current_domain_index = 0;
    private $uploaded_files_tracker = array(); // Track uploaded files to prevent duplicates
    
    /**
     * Constructor
     * @param AUI_Config_Manager $config_manager
     */
    public function __construct($config_manager) {
        $this->config_manager = $config_manager;
        
        // Add AJAX handlers
        add_action('wp_ajax_aui_upload_images', array($this, 'ajax_upload_images'));
        add_action('wp_ajax_aui_delete_uploaded_files', array($this, 'ajax_delete_uploaded_files'));
    }
    
    /**
     * AJAX handler for uploading images
     */
    public function ajax_upload_images() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'aui_nonce')) {
            wp_die('Security check failed');
        }

        // Check user capabilities
        if (!current_user_can('upload_files')) {
            wp_die('Insufficient permissions');
        }

        // Check if files were uploaded
        if (empty($_FILES['image_files']['name'][0])) {
            wp_send_json_error('No files uploaded');
        }

        $results = $this->process_uploaded_files($_FILES['image_files']);

        wp_send_json_success($results);
    }
    
    /**
     * AJAX handler for deleting uploaded files
     */
    public function ajax_delete_uploaded_files() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'aui_nonce')) {
            wp_die('Security check failed');
        }

        // Check user capabilities
        if (!current_user_can('upload_files')) {
            wp_die('Insufficient permissions');
        }

        $file_paths = isset($_POST['file_paths']) ? $_POST['file_paths'] : array();

        if (empty($file_paths)) {
            wp_send_json_error('No file paths provided');
        }

        $deleted_count = 0;
        $upload_dir = wp_upload_dir();
        $temp_dir = $upload_dir['basedir'] . '/aui-temp/';

        foreach ($file_paths as $file_path) {
            // Security check: only allow deletion from temp directory
            if (strpos($file_path, $temp_dir) === 0 && file_exists($file_path)) {
                if (unlink($file_path)) {
                    $deleted_count++;
                }
            }
        }

        // Clean up empty temp directory
        if (is_dir($temp_dir) && count(scandir($temp_dir)) == 2) { // Only . and ..
            rmdir($temp_dir);
        }

        wp_send_json_success(array(
            'deleted_count' => $deleted_count,
            'total_files' => count($file_paths)
        ));
    }
    
    /**
     * Process uploaded files
     * @param array $files
     * @return array
     */
    public function process_uploaded_files($files) {
        $upload_dir = wp_upload_dir();
        $temp_dir = $upload_dir['basedir'] . '/aui-temp/';

        // Create temp directory if not exists
        if (!file_exists($temp_dir)) {
            wp_mkdir_p($temp_dir);
        }

        $uploaded_files = array();
        $file_count = count($files['name']);
        $processed_files = array(); // Track processed files to prevent duplicates

        error_log("AUI Debug - process_uploaded_files called with $file_count files");
        for ($debug_i = 0; $debug_i < $file_count; $debug_i++) {
            error_log("AUI Debug - File #$debug_i: {$files['name'][$debug_i]}");
        }

        // Process each uploaded file
        for ($i = 0; $i < $file_count; $i++) {
            if ($files['error'][$i] !== UPLOAD_ERR_OK) {
                continue;
            }

            $original_filename = $files['name'][$i];
            $filename = sanitize_file_name($original_filename);
            $temp_file = $files['tmp_name'][$i];

            error_log("AUI Debug - Processing upload #$i:");
            error_log("AUI Debug - Original filename: $original_filename");
            error_log("AUI Debug - Sanitized filename: $filename");

            // Check if we've already processed this exact file
            $file_key = $filename . '_' . $files['size'][$i];
            if (isset($processed_files[$file_key])) {
                error_log("AUI Debug - Duplicate file detected: $filename (size: {$files['size'][$i]}), skipping");
                continue;
            }
            $processed_files[$file_key] = true;

            // Validate file type
            $file_type = wp_check_filetype($filename);
            if (!in_array($file_type['type'], array('image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'))) {
                error_log("AUI Debug - Invalid file type: {$file_type['type']}");
                continue;
            }

            // Always create unique filename to prevent any conflicts
            $file_info = pathinfo($filename);
            $name_without_ext = $file_info['filename'];
            $extension = isset($file_info['extension']) ? '.' . $file_info['extension'] : '';

            // Create unique filename with timestamp and random number
            $unique_suffix = '_' . time() . '_' . $i . '_' . rand(100, 999);
            $unique_filename = $name_without_ext . $unique_suffix . $extension;
            $temp_path = $temp_dir . $unique_filename;

            error_log("AUI Debug - Original filename: $filename");
            error_log("AUI Debug - Unique filename: $unique_filename");
            error_log("AUI Debug - Temp path: $temp_path");

            // Update filename to the unique one
            $filename = $unique_filename;

            if (move_uploaded_file($temp_file, $temp_path)) {
                // Create unique ID for this file to track it through the entire pipeline
                $file_unique_id = 'file_' . $i . '_' . time() . '_' . rand(1000, 9999);

                $uploaded_files[] = array(
                    'unique_id' => $file_unique_id,
                    'filename' => $original_filename, // Use original filename for display and API
                    'temp_filename' => $filename, // Unique filename for temp file
                    'path' => $temp_path,
                    'url' => $upload_dir['baseurl'] . '/aui-temp/' . $filename,
                    'size' => filesize($temp_path)
                );
                error_log("AUI Debug - Successfully moved file to: $temp_path");
                error_log("AUI Debug - File ID: $file_unique_id, Original: $original_filename, Temp: $filename");
            } else {
                error_log("AUI Debug - Failed to move file: $temp_file to $temp_path");
            }
        }

        if (empty($uploaded_files)) {
            return array(
                'success' => false,
                'message' => 'No valid image files uploaded',
                'results' => array()
            );
        }

        // Upload to APIs
        $results = $this->upload_multiple_images($uploaded_files);

        return $results;
    }

    /**
     * Upload multiple images
     * @param array $image_files
     * @return array
     */
    public function upload_multiple_images($image_files) {
        $this->upload_results = array();
        $this->uploaded_files_tracker = array(); // Reset tracker for new upload session
        $domains = $this->config_manager->get_domains();
        
        if (empty($domains)) {
            return array(
                'success' => false,
                'message' => 'No API domains configured',
                'results' => array()
            );
        }
        
        // Group images by pattern
        $grouped_images = $this->group_images_by_pattern($image_files);

        // Debug log grouping
        error_log('AUI Debug - Final grouped images:');
        $total_files_in_groups = 0;
        foreach ($grouped_images as $index => $group) {
            $filenames = array_map(function($file) { return $file['filename']; }, $group);
            error_log("AUI Debug - Group $index (" . count($group) . " files): " . implode(', ', $filenames));
            $total_files_in_groups += count($group);
        }
        error_log('AUI Debug - Total groups: ' . count($grouped_images));
        error_log('AUI Debug - Original files count: ' . count($image_files));
        error_log('AUI Debug - Total files in groups: ' . $total_files_in_groups);

        if ($total_files_in_groups !== count($image_files)) {
            error_log('AUI Debug - WARNING: File count mismatch! Some files may be duplicated or missing.');
        }
        
        $all_results = array();
        $successful_uploads = array();
        $failed_uploads = array();
        $processed_unique_ids = array(); // Track processed unique IDs to prevent duplicates
        
        foreach ($grouped_images as $group_index => $group) {
            error_log("AUI Debug - Starting upload for group $group_index with " . count($group) . " files");

            $group_results = $this->upload_image_group($group, $domains);

            error_log("AUI Debug - Group $group_index upload completed, got " . count($group_results) . " results");

            // Add group info to results for better display
            foreach ($group_results as &$result) {
                $result['group_index'] = $group_index;
                $result['group_size'] = count($group);
                $result_id = isset($result['unique_id']) ? $result['unique_id'] : 'no_id';
                error_log("AUI Debug - Result for {$result['filename']} (ID: $result_id): " . ($result['success'] ? 'SUCCESS' : 'FAILED'));
            }

            // Add results with duplicate prevention
            foreach ($group_results as $result) {
                $unique_id = isset($result['unique_id']) ? $result['unique_id'] : 'no_id_' . uniqid();

                // Check if this unique ID was already processed
                if (!in_array($unique_id, $processed_unique_ids)) {
                    $processed_unique_ids[] = $unique_id;
                    $all_results[] = $result;

                    if ($result['success']) {
                        $successful_uploads[] = $result;
                    } else {
                        $failed_uploads[] = $result;
                    }

                    error_log("AUI Debug - Added result for {$result['filename']} (ID: $unique_id)");
                } else {
                    error_log("AUI Debug - DUPLICATE DETECTED! Skipping result for {$result['filename']} (ID: $unique_id)");
                }
            }
        }

        error_log("AUI Debug - Total results: " . count($all_results));
        error_log("AUI Debug - Successful: " . count($successful_uploads));
        error_log("AUI Debug - Failed: " . count($failed_uploads));

        // Debug final results before sending to client
        error_log("AUI Debug - Final results being sent to client:");
        foreach ($all_results as $index => $result) {
            $unique_id = isset($result['unique_id']) ? $result['unique_id'] : 'no_id';
            error_log("AUI Debug - Final result #$index: {$result['filename']} (ID: $unique_id)");
        }

        // Sort results by group for better display
        usort($all_results, function($a, $b) {
            if ($a['group_index'] !== $b['group_index']) {
                return $a['group_index'] - $b['group_index'];
            }
            // Within same group, sort by filename
            return strcmp($a['filename'], $b['filename']);
        });
        
        return array(
            'success' => true,
            'message' => sprintf('Upload completed. Success: %d, Failed: %d', 
                count($successful_uploads), count($failed_uploads)),
            'results' => $all_results,
            'successful_uploads' => $successful_uploads,
            'failed_uploads' => $failed_uploads,
            'summary' => array(
                'total' => count($all_results),
                'successful' => count($successful_uploads),
                'failed' => count($failed_uploads)
            )
        );
    }
    
    /**
     * Group images by pattern with smart sorting
     * @param array $image_files
     * @return array
     */
    private function group_images_by_pattern($image_files) {
        // Step 1: Sort all files by filename first
        usort($image_files, function($a, $b) {
            return strcmp($a['filename'], $b['filename']);
        });

        error_log("AUI Debug - Files after sorting:");
        foreach ($image_files as $file) {
            error_log("AUI Debug - Sorted file: {$file['filename']}");
        }

        // Step 2: Group consecutive files with same base name
        $groups = array();
        $current_group = array();
        $current_base = null;

        foreach ($image_files as $file) {
            $filename = $file['filename'];
            $file_id = isset($file['unique_id']) ? $file['unique_id'] : 'no_id';
            $base_name = $this->extract_base_name($filename);

            error_log("AUI Debug - Processing file: $filename (ID: $file_id), base: $base_name");

            // If this is the first file or same base as current group
            if ($current_base === null || $current_base === $base_name) {
                $current_group[] = $file;
                $current_base = $base_name;
                error_log("AUI Debug - Added file $file_id to current group (base: $current_base)");
            } else {
                // Different base name, start new group
                if (!empty($current_group)) {
                    $groups[] = $current_group;
                    $group_ids = array_map(function($f) { return isset($f['unique_id']) ? $f['unique_id'] : 'no_id'; }, $current_group);
                    error_log("AUI Debug - Finished group with " . count($current_group) . " files: " . implode(', ', $group_ids));
                }
                $current_group = array($file);
                $current_base = $base_name;
                error_log("AUI Debug - Started new group with file $file_id (base: $current_base)");
            }
        }

        // Add the last group
        if (!empty($current_group)) {
            $groups[] = $current_group;
            error_log("AUI Debug - Added final group with " . count($current_group) . " files");
        }

        // Step 3: Sort within each group by number
        $self = $this; // For closure access
        foreach ($groups as $group_index => &$group) {
            usort($group, function($a, $b) use ($self) {
                $num_a = $self->extract_number_from_filename($a['filename']);
                $num_b = $self->extract_number_from_filename($b['filename']);
                return $num_a - $num_b;
            });

            error_log("AUI Debug - Group $group_index final order:");
            foreach ($group as $file) {
                error_log("AUI Debug - {$file['filename']}");
            }
        }

        return $groups;
    }

    /**
     * Extract base name from filename
     * ban-chai.png -> ban-chai
     * ban-chai_1.png -> ban-chai
     * ban-chai-k514-5578.png -> ban-chai-k514-5578
     */
    private function extract_base_name($filename) {
        // Remove extension first
        $name_without_ext = pathinfo($filename, PATHINFO_FILENAME);

        // Check if it has _number pattern at the end (only single digits or simple numbers)
        if (preg_match('/^(.+?)_(\d{1,3})$/', $name_without_ext, $matches)) {
            $base = $matches[1];
            $number = $matches[2];
            error_log("AUI Debug - extract_base_name: '$name_without_ext' -> base: '$base', number: '$number'");
            return $base; // Return base name without _number
        }

        error_log("AUI Debug - extract_base_name: '$name_without_ext' -> no pattern, using full name");
        return $name_without_ext; // Return as is if no _number pattern
    }

    /**
     * Extract number from filename for sorting
     * ban-chai.png -> 0
     * ban-chai_1.png -> 1
     * ban-chai-k514-5578.png -> 0 (not a simple _number pattern)
     */
    public function extract_number_from_filename($filename) {
        $name_without_ext = pathinfo($filename, PATHINFO_FILENAME);

        // Only extract simple _number patterns (1-3 digits)
        if (preg_match('/^.+?_(\d{1,3})$/', $name_without_ext, $matches)) {
            $number = intval($matches[1]);
            error_log("AUI Debug - Extracted number $number from '$name_without_ext'");
            return $number;
        }

        error_log("AUI Debug - No simple number pattern in '$name_without_ext', using 0");
        return 0; // Files without simple number pattern get 0 (will be sorted first)
    }
    
    /**
     * Upload a group of images to the same domain
     * @param array $image_group
     * @param array $domains
     * @return array
     */
    private function upload_image_group($image_group, $domains) {
        $domain = $this->get_next_domain($domains);
        $results = array();
        $delay_seconds = $this->config_manager->get_setting('delay_seconds', 1);

        error_log("AUI Debug - Uploading group of " . count($image_group) . " files to domain: $domain");

        // Debug: Log all files in this group before upload
        foreach ($image_group as $idx => $file) {
            error_log("AUI Debug - Group file #$idx: {$file['filename']} (path: {$file['path']})");
        }

        foreach ($image_group as $index => $file) {
            $file_id = isset($file['unique_id']) ? $file['unique_id'] : 'no_id';
            error_log("AUI Debug - Starting upload file #$index: {$file['filename']} (ID: $file_id)");
            error_log("AUI Debug - File path: {$file['path']}");
            error_log("AUI Debug - File URL: {$file['url']}");

            // Add delay between uploads to same domain (except first image)
            if ($index > 0 && count($image_group) > 1) {
                sleep($delay_seconds);
            }

            $result = $this->upload_single_image($file, $domain);

            if ($result !== null) {
                $results[] = $result;
                error_log("AUI Debug - Upload completed for {$file['filename']}: " . ($result['success'] ? 'SUCCESS' : 'FAILED'));
            } else {
                error_log("AUI Debug - ERROR: upload_single_image returned NULL for {$file['filename']}");
            }
            if (isset($result['response']['file_link'])) {
                error_log("AUI Debug - Generated link: {$result['response']['file_link']}");
            }
        }

        error_log("AUI Debug - Group upload completed. Total results: " . count($results));

        // Debug: Log each result before returning
        foreach ($results as $idx => $result) {
            if ($result !== null) {
                $result_id = isset($result['unique_id']) ? $result['unique_id'] : 'no_id';
                error_log("AUI Debug - Group result #$idx: {$result['filename']} (ID: $result_id) - " . ($result['success'] ? 'SUCCESS' : 'FAILED'));
            } else {
                error_log("AUI Debug - Group result #$idx: NULL RESULT!");
            }
        }

        return $results;
    }
    
    /**
     * Get next domain using round-robin
     * @param array $domains
     * @return string
     */
    private function get_next_domain($domains) {
        $domain = $domains[$this->current_domain_index % count($domains)];
        $this->current_domain_index++;
        return $domain;
    }
    
    /**
     * Upload single image with retry logic
     * @param array $file
     * @param string $domain
     * @return array
     */
    private function upload_single_image($file, $domain) {
        $retry_attempts = $this->config_manager->get_setting('retry_attempts', 2);
        $filename = $file['filename'];
        $file_path = $file['path'];
        $file_url = $file['url'];

        // Use unique_id as file identifier to prevent duplicates
        $file_identifier = isset($file['unique_id']) ? $file['unique_id'] : md5($filename . $file_path . microtime(true));

        error_log("AUI Debug - upload_single_image called for: $filename");
        error_log("AUI Debug - File path: $file_path");
        error_log("AUI Debug - File URL: $file_url");
        error_log("AUI Debug - File identifier: $file_identifier");

        // Check if this file has already been uploaded
        if (isset($this->uploaded_files_tracker[$file_identifier])) {
            error_log("AUI Debug - DUPLICATE DETECTED! File $filename (ID: $file_identifier) already uploaded, skipping");
            error_log("AUI Debug - Existing tracker keys: " . implode(', ', array_keys($this->uploaded_files_tracker)));
            return $this->uploaded_files_tracker[$file_identifier];
        } else {
            error_log("AUI Debug - File $filename (ID: $file_identifier) is NEW, proceeding with upload");
            error_log("AUI Debug - Current tracker keys: " . implode(', ', array_keys($this->uploaded_files_tracker)));
        }

        // Extract file info from original filename
        $file_info = pathinfo($filename);
        $name_without_ext = $file_info['filename'];
        $extension = isset($file_info['extension']) ? $file_info['extension'] : 'jpg';

        // Use original filename for API (no unique suffix)
        $api_name = $name_without_ext;

        error_log("AUI Debug - Original filename: $filename");
        error_log("AUI Debug - Name without ext: $name_without_ext");
        error_log("AUI Debug - API name: $api_name");
        error_log("AUI Debug - Extension: $extension");

        // Prepare API data
        $api_data = array(
            'upload_auto_delete' => 0,
            'type' => 'image/' . $extension,
            'nameFile' => $api_name,
            'extFile' => $extension,
            'linkImage' => $file_url,
            'size' => $file['size']
        );

        error_log("AUI Debug - API data prepared: " . json_encode($api_data));

        $last_error = '';

        // Try upload with retries
        for ($attempt = 0; $attempt <= $retry_attempts; $attempt++) {
            $result = $this->make_api_request($domain, $api_data);

            if ($result['success']) {
                error_log("AUI Debug - API Success for $filename");
                error_log("AUI Debug - API Response: " . json_encode($result['data']));

                $upload_result = array(
                    'success' => true,
                    'unique_id' => isset($file['unique_id']) ? $file['unique_id'] : 'no_id',
                    'original_path' => $file_path,
                    'original_url' => $file_url,
                    'filename' => $filename, // Use original filename
                    'domain' => $domain,
                    'attempt' => $attempt + 1,
                    'response' => $result['data']
                );

                // Delete temp file if auto_delete is enabled
                if ($this->config_manager->get_setting('auto_delete', 1)) {
                    if (file_exists($file_path)) {
                        unlink($file_path);
                        $upload_result['temp_file_deleted'] = true;
                    }
                }

                error_log("AUI Debug - Final upload result for $filename: " . json_encode($upload_result));

                // Store in tracker to prevent duplicates
                $this->uploaded_files_tracker[$file_identifier] = $upload_result;

                return $upload_result;
            } else {
                error_log("AUI Debug - API Failed for $filename: {$result['error']}");
            }

            $last_error = $result['error'];

            // Don't sleep after last attempt
            if ($attempt < $retry_attempts) {
                sleep(1); // Wait 1 second before retry
            }
        }

        // All attempts failed
        $failed_result = array(
            'success' => false,
            'unique_id' => isset($file['unique_id']) ? $file['unique_id'] : 'no_id',
            'original_path' => $file_path,
            'original_url' => $file_url,
            'filename' => $filename, // Use original filename
            'domain' => $domain,
            'attempts' => $retry_attempts + 1,
            'error' => $last_error
        );

        // Store failed result in tracker too
        $this->uploaded_files_tracker[$file_identifier] = $failed_result;

        return $failed_result;
    }
    
    /**
     * Make API request to upload image
     * @param string $domain
     * @param array $data
     * @return array
     */
    private function make_api_request($domain, $data) {
        $api_url = rtrim($domain, '/') . '/upload/api/upload-image';
        
        $response = wp_remote_post($api_url, array(
            'headers' => array(
                'Content-Type' => 'application/json',
            ),
            'body' => json_encode($data),
            'timeout' => 30,
            'sslverify' => false
        ));
        
        if (is_wp_error($response)) {
            return array(
                'success' => false,
                'error' => $response->get_error_message()
            );
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);
        
        if ($response_code !== 200) {
            return array(
                'success' => false,
                'error' => sprintf('HTTP %d: %s', $response_code, $response_body)
            );
        }
        
        $decoded_response = json_decode($response_body, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            return array(
                'success' => false,
                'error' => 'Invalid JSON response: ' . $response_body
            );
        }
        
        if (!isset($decoded_response['type']) || $decoded_response['type'] !== 'success') {
            $error_msg = isset($decoded_response['message']) ? $decoded_response['message'] : 'Unknown API error';
            return array(
                'success' => false,
                'error' => $error_msg
            );
        }
        
        return array(
            'success' => true,
            'data' => $decoded_response
        );
    }
}
