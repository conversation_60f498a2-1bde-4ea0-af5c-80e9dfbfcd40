<?php
/**
 * Image Uploader Class
 * Xử lý upload ảnh lên các domain API
 */

if (!defined('ABSPATH')) {
    exit;
}

class AUI_Image_Uploader {
    
    private $config_manager;
    private $upload_results = array();
    private $current_domain_index = 0;
    
    /**
     * Constructor
     * @param AUI_Config_Manager $config_manager
     */
    public function __construct($config_manager) {
        $this->config_manager = $config_manager;
        
        // Add AJAX handlers
        add_action('wp_ajax_aui_upload_images', array($this, 'ajax_upload_images'));
        add_action('wp_ajax_aui_delete_uploaded_files', array($this, 'ajax_delete_uploaded_files'));
    }
    
    /**
     * AJAX handler for uploading images
     */
    public function ajax_upload_images() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'aui_nonce')) {
            wp_die('Security check failed');
        }
        
        // Check user capabilities
        if (!current_user_can('upload_files')) {
            wp_die('Insufficient permissions');
        }
        
        $image_urls = isset($_POST['image_urls']) ? $_POST['image_urls'] : array();
        
        if (empty($image_urls)) {
            wp_send_json_error('No image URLs provided');
        }
        
        $results = $this->upload_multiple_images($image_urls);
        
        wp_send_json_success($results);
    }
    
    /**
     * AJAX handler for deleting uploaded files
     */
    public function ajax_delete_uploaded_files() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'aui_nonce')) {
            wp_die('Security check failed');
        }
        
        // Check user capabilities
        if (!current_user_can('upload_files')) {
            wp_die('Insufficient permissions');
        }
        
        $file_paths = isset($_POST['file_paths']) ? $_POST['file_paths'] : array();
        
        if (empty($file_paths)) {
            wp_send_json_error('No file paths provided');
        }
        
        $deleted_count = 0;
        foreach ($file_paths as $file_path) {
            if (file_exists($file_path) && unlink($file_path)) {
                $deleted_count++;
            }
        }
        
        wp_send_json_success(array(
            'deleted_count' => $deleted_count,
            'total_files' => count($file_paths)
        ));
    }
    
    /**
     * Upload multiple images
     * @param array $image_urls
     * @return array
     */
    public function upload_multiple_images($image_urls) {
        $this->upload_results = array();
        $domains = $this->config_manager->get_domains();
        
        if (empty($domains)) {
            return array(
                'success' => false,
                'message' => 'No API domains configured',
                'results' => array()
            );
        }
        
        // Group images by pattern
        $grouped_images = $this->group_images_by_pattern($image_urls);
        
        $all_results = array();
        $successful_uploads = array();
        $failed_uploads = array();
        
        foreach ($grouped_images as $group) {
            $group_results = $this->upload_image_group($group, $domains);
            $all_results = array_merge($all_results, $group_results);
            
            foreach ($group_results as $result) {
                if ($result['success']) {
                    $successful_uploads[] = $result;
                } else {
                    $failed_uploads[] = $result;
                }
            }
        }
        
        return array(
            'success' => true,
            'message' => sprintf('Upload completed. Success: %d, Failed: %d', 
                count($successful_uploads), count($failed_uploads)),
            'results' => $all_results,
            'successful_uploads' => $successful_uploads,
            'failed_uploads' => $failed_uploads,
            'summary' => array(
                'total' => count($all_results),
                'successful' => count($successful_uploads),
                'failed' => count($failed_uploads)
            )
        );
    }
    
    /**
     * Group images by pattern (_1, _2, _3)
     * @param array $image_urls
     * @return array
     */
    private function group_images_by_pattern($image_urls) {
        $groups = array();
        $pattern_groups = array();

        foreach ($image_urls as $url) {
            $filename = basename(parse_url($url, PHP_URL_PATH));

            // Check if filename matches pattern like abc_1, abc_2, abc_3
            if (preg_match('/^(.+)_(\d+)(\.[^.]+)?$/', $filename, $matches)) {
                $base_name = $matches[1];
                $number = intval($matches[2]);

                // Group by base name
                if (!isset($pattern_groups[$base_name])) {
                    $pattern_groups[$base_name] = array();
                }
                $pattern_groups[$base_name][] = array(
                    'url' => $url,
                    'number' => $number
                );
            } else {
                // Single image group
                $groups[] = array($url);
            }
        }

        // Sort pattern groups by number and add to main groups
        foreach ($pattern_groups as $base_name => $images) {
            // Sort by number
            usort($images, function($a, $b) {
                return $a['number'] - $b['number'];
            });

            // Extract URLs
            $sorted_urls = array_map(function($item) {
                return $item['url'];
            }, $images);

            $groups[] = $sorted_urls;
        }

        return $groups;
    }
    
    /**
     * Upload a group of images to the same domain
     * @param array $image_group
     * @param array $domains
     * @return array
     */
    private function upload_image_group($image_group, $domains) {
        $domain = $this->get_next_domain($domains);
        $results = array();
        $delay_seconds = $this->config_manager->get_setting('delay_seconds', 1);
        
        foreach ($image_group as $index => $image_url) {
            // Add delay between uploads to same domain (except first image)
            if ($index > 0 && count($image_group) > 1) {
                sleep($delay_seconds);
            }
            
            $result = $this->upload_single_image($image_url, $domain);
            $results[] = $result;
        }
        
        return $results;
    }
    
    /**
     * Get next domain using round-robin
     * @param array $domains
     * @return string
     */
    private function get_next_domain($domains) {
        $domain = $domains[$this->current_domain_index % count($domains)];
        $this->current_domain_index++;
        return $domain;
    }
    
    /**
     * Upload single image with retry logic
     * @param string $image_url
     * @param string $domain
     * @return array
     */
    private function upload_single_image($image_url, $domain) {
        $retry_attempts = $this->config_manager->get_setting('retry_attempts', 2);
        $filename = basename(parse_url($image_url, PHP_URL_PATH));
        
        // Extract file info
        $file_info = pathinfo($filename);
        $name_without_ext = $file_info['filename'];
        $extension = isset($file_info['extension']) ? $file_info['extension'] : 'jpg';
        
        // Prepare API data
        $api_data = array(
            'upload_auto_delete' => 0,
            'type' => 'image/' . $extension,
            'nameFile' => $name_without_ext,
            'extFile' => $extension,
            'linkImage' => $image_url,
            'size' => 0
        );
        
        $last_error = '';
        
        // Try upload with retries
        for ($attempt = 0; $attempt <= $retry_attempts; $attempt++) {
            $result = $this->make_api_request($domain, $api_data);
            
            if ($result['success']) {
                return array(
                    'success' => true,
                    'original_url' => $image_url,
                    'filename' => $filename,
                    'domain' => $domain,
                    'attempt' => $attempt + 1,
                    'response' => $result['data']
                );
            }
            
            $last_error = $result['error'];
            
            // Don't sleep after last attempt
            if ($attempt < $retry_attempts) {
                sleep(1); // Wait 1 second before retry
            }
        }
        
        return array(
            'success' => false,
            'original_url' => $image_url,
            'filename' => $filename,
            'domain' => $domain,
            'attempts' => $retry_attempts + 1,
            'error' => $last_error
        );
    }
    
    /**
     * Make API request to upload image
     * @param string $domain
     * @param array $data
     * @return array
     */
    private function make_api_request($domain, $data) {
        $api_url = rtrim($domain, '/') . '/upload/api/upload-image';
        
        $response = wp_remote_post($api_url, array(
            'headers' => array(
                'Content-Type' => 'application/json',
            ),
            'body' => json_encode($data),
            'timeout' => 30,
            'sslverify' => false
        ));
        
        if (is_wp_error($response)) {
            return array(
                'success' => false,
                'error' => $response->get_error_message()
            );
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);
        
        if ($response_code !== 200) {
            return array(
                'success' => false,
                'error' => sprintf('HTTP %d: %s', $response_code, $response_body)
            );
        }
        
        $decoded_response = json_decode($response_body, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            return array(
                'success' => false,
                'error' => 'Invalid JSON response: ' . $response_body
            );
        }
        
        if (!isset($decoded_response['type']) || $decoded_response['type'] !== 'success') {
            $error_msg = isset($decoded_response['message']) ? $decoded_response['message'] : 'Unknown API error';
            return array(
                'success' => false,
                'error' => $error_msg
            );
        }
        
        return array(
            'success' => true,
            'data' => $decoded_response
        );
    }
}
