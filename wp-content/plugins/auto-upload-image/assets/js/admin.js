/**
 * Auto Upload Image Admin JavaScript
 */

jQuery(document).ready(function($) {

    var uploadInProgress = false;
    var uploadResults = [];
    var successfulFiles = [];
    var selectedFiles = [];

    /**
     * Initialize file upload functionality
     */
    initFileUpload();

    /**
     * Initialize file upload drag & drop
     */
    function initFileUpload() {
        var $dropZone = $('#aui-drop-zone');
        var $fileInput = $('#aui-image-files');
        var $browseLink = $('#aui-browse-files');
        var $submitBtn = $('button[type="submit"]');

        // Browse files click
        $browseLink.on('click', function(e) {
            e.preventDefault();
            $fileInput.click();
        });

        // Drop zone click
        $dropZone.on('click', function() {
            $fileInput.click();
        });

        // File input change
        $fileInput.on('change', function() {
            handleFiles(this.files);
        });

        // Drag & drop events
        $dropZone.on('dragover dragenter', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(this).addClass('dragover');
        });

        $dropZone.on('dragleave', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(this).removeClass('dragover');
        });

        $dropZone.on('drop', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(this).removeClass('dragover');

            var files = e.originalEvent.dataTransfer.files;
            handleFiles(files);
        });

        // Clear files button
        $('#aui-clear-files').on('click', function() {
            clearSelectedFiles();
        });

        // Test button for All Links
        $('#aui-show-test-links').on('click', function() {
            $('#aui-test-all-links').toggle();
            setupCopyHandlers();
        });
    }

    /**
     * Handle selected files
     */
    function handleFiles(files) {
        for (var i = 0; i < files.length; i++) {
            var file = files[i];

            // Check if it's an image
            if (!file.type.match('image.*')) {
                alert('File "' + file.name + '" không phải là ảnh!');
                continue;
            }

            // Check file size (max 10MB)
            if (file.size > 10 * 1024 * 1024) {
                alert('File "' + file.name + '" quá lớn (tối đa 10MB)!');
                continue;
            }

            // Check if already selected
            var exists = selectedFiles.some(function(f) {
                return f.name === file.name && f.size === file.size;
            });

            if (!exists) {
                selectedFiles.push(file);
            }
        }

        updateFileList();
        updateSubmitButton();
    }

    /**
     * Update file list display
     */
    function updateFileList() {
        var $container = $('#aui-selected-files');
        $container.empty();

        selectedFiles.forEach(function(file, index) {
            var $item = $('<div class="aui-file-item">');

            // Create preview
            var $preview = $('<img class="aui-file-preview">');
            var reader = new FileReader();
            reader.onload = function(e) {
                $preview.attr('src', e.target.result);
            };
            reader.readAsDataURL(file);

            // File info
            var $info = $('<div class="aui-file-info">');
            $info.append('<div class="aui-file-name">' + file.name + '</div>');
            $info.append('<div class="aui-file-size">' + formatFileSize(file.size) + '</div>');

            // Remove button
            var $remove = $('<button class="aui-file-remove" data-index="' + index + '">Xóa</button>');
            $remove.on('click', function() {
                removeFile($(this).data('index'));
            });

            $item.append($preview).append($info).append($remove);
            $container.append($item);
        });
    }

    /**
     * Remove file from selection
     */
    function removeFile(index) {
        selectedFiles.splice(index, 1);
        updateFileList();
        updateSubmitButton();
    }

    /**
     * Clear all selected files
     */
    function clearSelectedFiles() {
        selectedFiles = [];
        $('#aui-selected-files').empty();
        $('#aui-image-files').val('');
        updateSubmitButton();
    }

    /**
     * Update submit button state
     */
    function updateSubmitButton() {
        var $submitBtn = $('button[type="submit"]');
        if (selectedFiles.length > 0) {
            $submitBtn.prop('disabled', false).text('Upload ' + selectedFiles.length + ' ảnh');
        } else {
            $submitBtn.prop('disabled', true).text('Upload Ảnh');
        }
    }

    /**
     * Handle upload form submission
     */
    $('#aui-upload-form').on('submit', function(e) {
        e.preventDefault();

        if (uploadInProgress) {
            return;
        }

        if (selectedFiles.length === 0) {
            alert('Vui lòng chọn ít nhất một ảnh!');
            return;
        }

        startUpload();
    });
    
    /**
     * Start upload process
     */
    function startUpload() {
        uploadInProgress = true;
        uploadResults = [];
        successfulFiles = [];

        // Update UI
        $('#aui-upload-status').html('<span class="aui-loading">Đang upload ' + selectedFiles.length + ' ảnh...</span>');
        $('button[type="submit"]').prop('disabled', true).addClass('loading');
        $('#aui-upload-results').hide();

        // Show progress
        showProgress(0, selectedFiles.length);

        // Create FormData
        var formData = new FormData();
        formData.append('action', 'aui_upload_images');
        formData.append('nonce', aui_ajax.nonce);

        // Add files to FormData
        selectedFiles.forEach(function(file, index) {
            formData.append('image_files[]', file);
        });

        // Make AJAX request
        $.ajax({
            url: aui_ajax.ajax_url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    handleUploadSuccess(response.data);
                } else {
                    handleUploadError(response.data || 'Unknown error');
                }
            },
            error: function(xhr, status, error) {
                handleUploadError('AJAX Error: ' + error);
            },
            complete: function() {
                uploadInProgress = false;
                updateSubmitButton();
                $('button[type="submit"]').removeClass('loading');
                hideProgress();
            }
        });
    }
    
    /**
     * Handle successful upload response
     */
    function handleUploadSuccess(data) {
        uploadResults = data.results || [];
        successfulFiles = data.successful_uploads || [];

        // Debug log
        console.log('Upload response data:', data);
        console.log('Upload results:', uploadResults);
        console.log('Successful files:', successfulFiles);

        $('#aui-upload-status').html('<span class="aui-success">' + data.message + '</span>');

        displayResults(data);
    }
    
    /**
     * Handle upload error
     */
    function handleUploadError(error) {
        $('#aui-upload-status').html('<span class="aui-error">Lỗi: ' + error + '</span>');
    }
    
    /**
     * Display upload results
     */
    function displayResults(data) {
        var html = '';
        
        // Summary
        if (data.summary) {
            html += '<div class="aui-summary">';
            html += '<h4>Tổng kết</h4>';
            html += '<div class="aui-summary-stats">';
            html += '<div class="aui-summary-stat total">';
            html += '<span class="number">' + data.summary.total + '</span>';
            html += '<span class="label">Tổng</span>';
            html += '</div>';
            html += '<div class="aui-summary-stat success">';
            html += '<span class="number">' + data.summary.successful + '</span>';
            html += '<span class="label">Thành công</span>';
            html += '</div>';
            html += '<div class="aui-summary-stat error">';
            html += '<span class="number">' + data.summary.failed + '</span>';
            html += '<span class="label">Thất bại</span>';
            html += '</div>';
            html += '</div>';
            html += '</div>';
        }

        // All Links section - collect from all successful results
        var allLinks = [];
        console.log('Checking for links in uploadResults:', uploadResults);

        if (uploadResults && uploadResults.length > 0) {
            uploadResults.forEach(function(result) {
                console.log('Processing result:', result);
                if (result.success && result.response && result.response.file_link) {
                    allLinks.push(result.response.file_link);
                    console.log('Added link:', result.response.file_link);
                }
            });
        }

        console.log('All links collected:', allLinks);

        // Always show All Links section if we have any links
        if (allLinks.length > 0) {
            html += '<div class="aui-all-links">';
            html += '<h4>📋 All Links (' + allLinks.length + ' links)</h4>';
            html += '<div class="aui-links-container">';
            html += '<textarea id="aui-all-links-textarea" readonly placeholder="Danh sách link sẽ hiển thị ở đây...">' + allLinks.join('\n') + '</textarea>';
            html += '<div class="aui-links-actions">';
            html += '<button id="aui-copy-all-links" class="button button-primary" title="Copy tất cả link vào clipboard">📋 Copy toàn bộ</button>';
            html += '<button id="aui-select-all-links" class="button button-secondary" title="Chọn tất cả text">🔍 Select All</button>';
            html += '<span class="description">Click vào textarea để chọn tất cả link</span>';
            html += '</div>';
            html += '</div>';
            html += '</div>';
            console.log('All Links HTML added to output');
        } else {
            console.log('No links found to display');
            // Show empty section for debugging
            html += '<div class="aui-all-links">';
            html += '<h4>📋 All Links (0 links)</h4>';
            html += '<p>Không có link nào được tạo thành công.</p>';
            html += '</div>';
        }
        
        // Results
        if (uploadResults.length > 0) {
            uploadResults.forEach(function(result) {
                html += '<div class="aui-result-item ' + (result.success ? 'aui-result-success' : 'aui-result-error') + '">';
                
                // Header
                html += '<div class="aui-result-header">';
                html += '<span>' + result.filename + '</span>';
                html += '<span class="aui-result-domain">' + result.domain + '</span>';
                html += '</div>';
                
                if (result.success) {
                    // Success result
                    html += '<div>✓ Upload thành công';
                    if (result.attempt > 1) {
                        html += ' (sau ' + result.attempt + ' lần thử)';
                    }
                    if (result.temp_file_deleted) {
                        html += ' - File VPS đã xóa';
                    }
                    html += '</div>';

                    if (result.response) {
                        html += '<div class="aui-result-links">';
                        if (result.response.file_link) {
                            html += '<a href="' + result.response.file_link + '" target="_blank">File Link</a>';
                        }
                        if (result.response.direct_link) {
                            html += '<a href="' + result.response.direct_link + '" target="_blank">Direct Link</a>';
                        }
                        html += '</div>';

                        // Show file info
                        html += '<div class="aui-file-info-result">';
                        html += '<small>File ID: ' + (result.response.file_id || 'N/A') + '</small>';
                        html += '</div>';
                    }
                } else {
                    // Error result
                    html += '<div>✗ Upload thất bại';
                    if (result.attempts > 1) {
                        html += ' (đã thử ' + result.attempts + ' lần)';
                    }
                    html += '</div>';

                    if (result.error) {
                        html += '<div class="aui-result-error-msg">' + escapeHtml(result.error) + '</div>';
                    }
                }
                
                html += '</div>';
            });
        }
        
        $('#aui-results-content').html(html);
        $('#aui-upload-results').show();
        
        // Show delete button if there are successful uploads
        if (successfulFiles.length > 0) {
            $('#aui-delete-files').show();
        } else {
            $('#aui-delete-files').hide();
        }

        // Add event handlers for copy buttons
        setupCopyHandlers();
    }
    
    /**
     * Handle delete uploaded files
     */
    $('#aui-delete-files').on('click', function() {
        if (successfulFiles.length === 0) {
            alert('Không có file nào để xóa!');
            return;
        }
        
        if (!confirm('Bạn có chắc muốn xóa ' + successfulFiles.length + ' file đã upload thành công?')) {
            return;
        }
        
        var filePaths = successfulFiles.map(function(file) {
            return file.original_path; // Use file path instead of URL
        });
        
        $(this).prop('disabled', true).text('Đang xóa...');
        
        $.ajax({
            url: aui_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'aui_delete_uploaded_files',
                nonce: aui_ajax.nonce,
                file_paths: filePaths
            },
            success: function(response) {
                if (response.success) {
                    alert('Đã xóa ' + response.data.deleted_count + '/' + response.data.total_files + ' file!');
                    $('#aui-delete-files').hide();
                } else {
                    alert('Lỗi khi xóa file: ' + (response.data || 'Unknown error'));
                }
            },
            error: function(xhr, status, error) {
                alert('AJAX Error: ' + error);
            },
            complete: function() {
                $('#aui-delete-files').prop('disabled', false).text('Xóa file đã upload thành công');
            }
        });
    });
    
    /**
     * Show progress bar
     */
    function showProgress(current, total) {
        var percent = total > 0 ? Math.round((current / total) * 100) : 0;
        
        if ($('.aui-progress').length === 0) {
            var progressHtml = '<div class="aui-progress">' +
                '<div class="aui-progress-bar">' +
                '<span class="aui-progress-text">0%</span>' +
                '</div>' +
                '</div>';
            $('#aui-upload-form').after(progressHtml);
        }
        
        $('.aui-progress-bar').css('width', percent + '%');
        $('.aui-progress-text').text(percent + '%');
    }
    
    /**
     * Hide progress bar
     */
    function hideProgress() {
        $('.aui-progress').remove();
    }
    
    /**
     * Validate URL
     */
    function isValidUrl(string) {
        try {
            new URL(string);
            return true;
        } catch (_) {
            return false;
        }
    }
    
    /**
     * Escape HTML
     */
    function escapeHtml(text) {
        var map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text.replace(/[&<>"']/g, function(m) { return map[m]; });
    }

    /**
     * Format file size
     */
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';

        var k = 1024;
        var sizes = ['Bytes', 'KB', 'MB', 'GB'];
        var i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Setup copy handlers for all links
     */
    function setupCopyHandlers() {
        // Copy all links button
        $('#aui-copy-all-links').off('click').on('click', function() {
            var $textarea = $('#aui-all-links-textarea');
            var $button = $(this);
            var text = $textarea.val();

            if (!text) {
                alert('Không có link nào để copy!');
                return;
            }

            var originalText = $button.text();
            $button.prop('disabled', true);

            // Try modern Clipboard API first
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(text).then(function() {
                    showCopySuccess($button, originalText);
                }).catch(function(err) {
                    console.log('Clipboard API failed:', err);
                    fallbackCopy($textarea, $button, originalText);
                });
            } else {
                // Fallback to older method
                fallbackCopy($textarea, $button, originalText);
            }
        });

        // Select all links button
        $('#aui-select-all-links').off('click').on('click', function() {
            var $textarea = $('#aui-all-links-textarea');
            if ($textarea.length > 0) {
                $textarea.select();
                $textarea.focus();
            }
        });

        // Auto-select on textarea click
        $('#aui-all-links-textarea').off('click').on('click', function() {
            $(this).select();
        });
    }

    /**
     * Show copy success feedback
     */
    function showCopySuccess($button, originalText) {
        $button.text('✓ Đã copy!').addClass('copied');
        setTimeout(function() {
            $button.text(originalText).removeClass('copied').prop('disabled', false);
        }, 2000);
    }

    /**
     * Fallback copy method for older browsers
     */
    function fallbackCopy($textarea, $button, originalText) {
        try {
            $textarea.select();
            $textarea[0].setSelectionRange(0, 99999); // For mobile devices

            var successful = document.execCommand('copy');
            if (successful) {
                showCopySuccess($button, originalText);
            } else {
                $button.prop('disabled', false);
                alert('Không thể copy tự động. Vui lòng chọn text và nhấn Ctrl+C.');
            }
        } catch (err) {
            $button.prop('disabled', false);
            $textarea.select();
            alert('Vui lòng chọn text và nhấn Ctrl+C (hoặc Cmd+C) để copy.');
        }
    }
    
    /**
     * Auto-resize textarea
     */
    $('#aui-image-urls').on('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });
    
    /**
     * Domain management (for config page)
     */
    if ($('#aui-add-domain').length > 0) {
        // Add domain
        $('#aui-add-domain').click(function() {
            var newDomain = '<div class="aui-domain-item">' +
                '<input type="url" name="domains[]" value="" placeholder="https://example.com" />' +
                '<button type="button" class="button aui-remove-domain">Xóa</button>' +
                '</div>';
            $('#aui-domains-list').append(newDomain);
        });
        
        // Remove domain
        $(document).on('click', '.aui-remove-domain', function() {
            if ($('.aui-domain-item').length > 1) {
                $(this).parent().remove();
            } else {
                alert('Phải có ít nhất một domain!');
            }
        });
        
        // Validate domain on input
        $(document).on('input', 'input[name="domains[]"]', function() {
            var $input = $(this);
            var url = $input.val().trim();
            
            if (url && !isValidUrl(url)) {
                $input.css('border-color', '#dc3232');
            } else {
                $input.css('border-color', '');
            }
        });
    }
    
    /**
     * Form validation
     */
    $('form').on('submit', function() {
        var isValid = true;
        
        // Validate domain URLs
        $('input[name="domains[]"]').each(function() {
            var url = $(this).val().trim();
            if (url && !isValidUrl(url)) {
                $(this).css('border-color', '#dc3232');
                isValid = false;
            }
        });
        
        if (!isValid) {
            alert('Vui lòng kiểm tra lại các URL domain!');
            return false;
        }
        
        return true;
    });
    
});
