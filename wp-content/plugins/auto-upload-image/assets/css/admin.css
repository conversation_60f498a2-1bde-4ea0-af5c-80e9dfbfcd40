/* Auto Upload Image Admin Styles */

.aui-upload-section {
    background: #fff;
    padding: 20px;
    margin: 20px 0;
    border: 1px solid #ccd0d4;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
    border-radius: 4px;
}

.aui-info-section {
    background: #fff;
    padding: 20px;
    margin: 20px 0;
    border: 1px solid #ccd0d4;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
    border-radius: 4px;
}

/* File upload area */
.aui-file-upload-area {
    width: 100%;
    max-width: 800px;
}

.aui-drop-zone {
    border: 2px dashed #ccc;
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    background: #fafafa;
    transition: all 0.3s ease;
    cursor: pointer;
}

.aui-drop-zone:hover,
.aui-drop-zone.dragover {
    border-color: #0073aa;
    background: #f0f8ff;
}

.aui-drop-zone-content .dashicons {
    font-size: 48px;
    color: #ccc;
    margin-bottom: 10px;
}

.aui-drop-zone:hover .dashicons,
.aui-drop-zone.dragover .dashicons {
    color: #0073aa;
}

.aui-drop-zone p {
    margin: 10px 0;
    font-size: 16px;
}

.aui-drop-zone a {
    color: #0073aa;
    text-decoration: none;
}

.aui-drop-zone a:hover {
    text-decoration: underline;
}

.aui-selected-files {
    margin-top: 20px;
    max-height: 300px;
    overflow-y: auto;
}

.aui-file-item {
    display: flex;
    align-items: center;
    padding: 10px;
    margin: 5px 0;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.aui-file-preview {
    width: 50px;
    height: 50px;
    margin-right: 15px;
    border-radius: 4px;
    object-fit: cover;
    border: 1px solid #ddd;
}

.aui-file-info {
    flex: 1;
}

.aui-file-name {
    font-weight: bold;
    margin-bottom: 5px;
}

.aui-file-size {
    font-size: 12px;
    color: #666;
}

.aui-file-remove {
    background: #dc3232;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
}

.aui-file-remove:hover {
    background: #a00;
}

#aui-upload-form {
    margin-bottom: 20px;
}

#aui-upload-status {
    margin-left: 10px;
    font-weight: bold;
    display: inline-block;
    min-width: 150px;
}

.aui-loading {
    color: #0073aa;
}

.aui-loading:before {
    content: "⟳ ";
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.aui-success {
    color: #46b450;
}

.aui-success:before {
    content: "✓ ";
}

.aui-error {
    color: #dc3232;
}

.aui-error:before {
    content: "✗ ";
}

#aui-upload-results {
    margin-top: 30px;
    padding: 20px;
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.aui-result-item {
    padding: 15px;
    margin: 10px 0;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #fff;
}

.aui-result-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.aui-result-error {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.aui-result-header {
    font-weight: bold;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.aui-result-domain {
    font-size: 12px;
    background: #007cba;
    color: white;
    padding: 2px 8px;
    border-radius: 3px;
}

.aui-result-links {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid rgba(0,0,0,0.1);
}

.aui-result-links a {
    display: inline-block;
    margin-right: 15px;
    padding: 5px 10px;
    background: #0073aa;
    color: white;
    text-decoration: none;
    border-radius: 3px;
    font-size: 12px;
}

.aui-result-links a:hover {
    background: #005a87;
}

.aui-result-error-msg {
    margin-top: 10px;
    padding: 10px;
    background: rgba(220, 50, 50, 0.1);
    border-radius: 3px;
    font-family: monospace;
    font-size: 12px;
}

.aui-file-info-result {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid rgba(0,0,0,0.1);
    font-size: 12px;
    color: #666;
}

/* All Links section */
.aui-all-links {
    margin: 20px 0;
    padding: 20px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
}

.aui-all-links h4 {
    margin: 0 0 15px 0;
    color: #495057;
    font-size: 16px;
}

.aui-links-container {
    position: relative;
}

#aui-all-links-textarea {
    width: 100%;
    height: 150px;
    padding: 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-family: monospace;
    font-size: 13px;
    line-height: 1.4;
    background: #ffffff;
    resize: vertical;
    box-sizing: border-box;
}

#aui-all-links-textarea:focus {
    outline: none;
    border-color: #0073aa;
    box-shadow: 0 0 0 2px rgba(0, 115, 170, 0.2);
}

.aui-links-actions {
    margin-top: 10px;
    display: flex;
    gap: 10px;
    align-items: center;
}

#aui-copy-all-links {
    background: #0073aa;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    transition: all 0.2s ease;
}

#aui-copy-all-links:hover {
    background: #005a87;
}

#aui-copy-all-links.copied {
    background: #46b450;
}

#aui-select-all-links {
    background: #6c757d;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
}

#aui-select-all-links:hover {
    background: #5a6268;
}

.aui-links-actions .description {
    font-size: 12px;
    color: #6c757d;
    margin-left: auto;
}

.aui-summary {
    padding: 15px;
    margin-bottom: 20px;
    background: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 4px;
}

.aui-summary-stats {
    display: flex;
    gap: 20px;
    margin-top: 10px;
}

.aui-summary-stat {
    padding: 10px;
    background: white;
    border-radius: 3px;
    text-align: center;
    min-width: 80px;
}

.aui-summary-stat .number {
    font-size: 24px;
    font-weight: bold;
    display: block;
}

.aui-summary-stat .label {
    font-size: 12px;
    color: #666;
}

.aui-summary-stat.success .number {
    color: #46b450;
}

.aui-summary-stat.error .number {
    color: #dc3232;
}

.aui-summary-stat.total .number {
    color: #0073aa;
}

/* Domain configuration styles */
.aui-domain-item {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.aui-domain-item input[type="url"] {
    flex: 1;
    max-width: 400px;
}

.aui-remove-domain {
    background: #dc3232;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 3px;
    cursor: pointer;
}

.aui-remove-domain:hover {
    background: #a00;
}

#aui-add-domain {
    background: #0073aa;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 3px;
    cursor: pointer;
}

#aui-add-domain:hover {
    background: #005a87;
}

/* Progress indicator */
.aui-progress {
    margin: 20px 0;
    background: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
    height: 20px;
}

.aui-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #0073aa, #005a87);
    width: 0%;
    transition: width 0.3s ease;
    position: relative;
}

.aui-progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* Responsive design */
@media (max-width: 768px) {
    .aui-upload-section,
    .aui-info-section {
        padding: 15px;
        margin: 15px 0;
    }
    
    #aui-image-urls {
        max-width: 100%;
    }
    
    .aui-result-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
    
    .aui-summary-stats {
        flex-direction: column;
        gap: 10px;
    }
    
    .aui-domain-item {
        flex-direction: column;
        align-items: stretch;
    }
    
    .aui-domain-item input[type="url"] {
        max-width: 100%;
    }

    .aui-links-actions {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .aui-links-actions .description {
        margin-left: 0;
        text-align: center;
    }

    #aui-all-links-textarea {
        height: 120px;
        font-size: 12px;
    }
}

/* Loading animation for buttons */
.button.loading {
    position: relative;
    color: transparent !important;
}

.button.loading:after {
    content: "";
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border-radius: 50%;
    border: 2px solid transparent;
    border-top-color: #ffffff;
    animation: spin 1s linear infinite;
}

/* Tooltip styles */
.aui-tooltip {
    position: relative;
    cursor: help;
}

.aui-tooltip:hover:after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #333;
    color: white;
    padding: 5px 10px;
    border-radius: 3px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
}

.aui-tooltip:hover:before {
    content: "";
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: #333;
    margin-bottom: -5px;
}
