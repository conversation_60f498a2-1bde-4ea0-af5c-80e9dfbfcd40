# Auto Upload Image Plugin

Plugin WordPress để upload ảnh tự động lên nhiều domain API với tính năng phân phối và nhóm ảnh thông minh.

## Tính năng chính

### 1. Quản lý nhiều Domain API
- Cấu hình nhiều domain API (mặc định: https://cn-av.com, https://hentai-sub.com)
- Thêm/xóa domain dễ dàng qua giao diện admin
- Validate URL domain tự động

### 2. Upload ảnh thông minh
- **Round-robin distribution**: Phân phối ảnh đều giữa các domain
- **Nhóm ảnh theo pattern**: Ảnh có tên dạng `abc_1`, `abc_2`, `abc_3` sẽ được upload cùng một domain
- **Retry logic**: Thử lại tối đa 2 lần khi upload thất bại
- **Delay protection**: Delay 1 giây giữa các upload cùng domain để tránh bị chặn

### 3. Giao diện quản trị
- Trang upload ảnh với drag & drop
- Hiển thị kết quả upload chi tiết
- **All Links**: Textarea hiển thị tất cả link thành công để copy hàng loạt
- **Copy toàn bộ**: Nút copy tất cả link cùng lúc
- Trang cấu hình domain và settings
- Responsive design

### 4. API Integration
- Tương thích với API format của cn-av.com và hentai-sub.com
- Xử lý response JSON
- Error handling chi tiết

## Cài đặt

1. Upload thư mục plugin vào `/wp-content/plugins/`
2. Kích hoạt plugin trong WordPress Admin
3. Truy cập menu "Auto Upload Image" để sử dụng

## Sử dụng

### Cấu hình Domain API

1. Vào **Auto Upload Image > Cấu hình API**
2. Thêm/sửa/xóa các domain API
3. Cấu hình settings:
   - **Số lần thử lại**: 0-10 lần
   - **Delay giữa upload**: 0-60 giây
   - **Tự động xóa file**: Có/Không

### Upload ảnh

1. Vào **Auto Upload Image**
2. Kéo thả ảnh vào vùng upload hoặc click "chọn file"
3. Chọn nhiều ảnh cùng lúc
4. Click "Upload Ảnh"
5. Xem kết quả upload chi tiết
6. **Copy link**: Sử dụng textarea "All Links" để copy tất cả link thành công
7. (Tùy chọn) Click "Xóa file VPS đã upload thành công"

### Ví dụ URL input:
```
https://example.com/image1.jpg
https://example.com/image2.jpg
https://example.com/abc_1.jpg
https://example.com/abc_2.jpg
https://example.com/abc_3.jpg
```

## Thuật toán phân phối

### Round-robin cơ bản:
- Ảnh 1 → Domain 1
- Ảnh 2 → Domain 2  
- Ảnh 3 → Domain 1
- Ảnh 4 → Domain 2

### Nhóm ảnh theo pattern:
Ảnh có cùng tên gốc sẽ được nhóm lại:
- `ban-chai.png`, `ban-chai_1.png`, `ban-chai_3.png` → cùng 1 domain
- `photo.jpg`, `photo_1.jpg`, `photo_2.jpg` → cùng 1 domain
- Hiển thị kết quả theo nhóm với header riêng biệt

## API Format

### Request:
```json
{
    "upload_auto_delete": 0,
    "type": "image/jpg",
    "nameFile": "landscape-photography_1645-t",
    "extFile": "jpg", 
    "linkImage": "https://example.com/image.jpg",
    "size": 0
}
```

### Response thành công:
```json
{
    "type": "success",
    "file_name": "ofrkPFAvzTWTVzp-landscape-photography_1645-t.jpg",
    "file_id": "ofrkPFAvzTWTVzp-landscape-photography_1645-t.jpg",
    "file_link": "https://cn-av.com/upload/ofrkPFAvzTWTVzp-landscape-photography_1645-t.jpg",
    "direct_link": "https://cn-av.com/upload/Application/storage/app/public/uploads/users/aQ2WVGrBGkx7y/landscape-photography_1645-t.jpg"
}
```

## Cấu trúc Plugin

```
auto-upload-image/
├── auto-upload-image.php          # File plugin chính
├── includes/
│   ├── class-config-manager.php   # Quản lý cấu hình
│   ├── class-image-uploader.php   # Xử lý upload
│   └── class-admin-page.php       # Giao diện admin
├── assets/
│   ├── css/
│   │   └── admin.css             # CSS cho admin
│   └── js/
│       └── admin.js              # JavaScript cho admin
└── README.md                     # Tài liệu này
```

## Classes chính

### AutoUploadImage
- Class chính khởi tạo plugin
- Quản lý lifecycle và dependencies

### AUI_Config_Manager  
- Quản lý cấu hình domain API
- Lưu/đọc settings từ WordPress options
- Validate domain URLs

### AUI_Image_Uploader
- Xử lý logic upload ảnh
- Thuật toán round-robin và nhóm ảnh
- Retry logic và error handling
- AJAX handlers

### AUI_Admin_Page
- Tạo menu và trang admin
- Render giao diện upload và cấu hình
- Xử lý form submissions

## Hooks và Filters

### Actions:
- `wp_ajax_aui_upload_images` - AJAX upload ảnh
- `wp_ajax_aui_delete_uploaded_files` - AJAX xóa file

### WordPress Hooks:
- `admin_menu` - Tạo menu admin
- `admin_enqueue_scripts` - Load CSS/JS
- `admin_init` - Xử lý form submissions

## Security

- Nonce verification cho tất cả AJAX requests
- Capability checks (`manage_options`, `upload_files`)
- URL validation và sanitization
- Escape output data

## Yêu cầu hệ thống

- WordPress 5.0+
- PHP 7.0+
- cURL extension
- JSON extension

## Changelog

### Version 1.0.0
- Phiên bản đầu tiên
- Tất cả tính năng cơ bản
- Giao diện admin hoàn chỉnh
- API integration

## Support

Để được hỗ trợ, vui lòng tạo issue hoặc liên hệ developer.

## License

GPL v2 or later
