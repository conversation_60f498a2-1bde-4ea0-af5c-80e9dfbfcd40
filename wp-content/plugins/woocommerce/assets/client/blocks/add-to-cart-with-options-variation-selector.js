(()=>{var e,t,o,r={6911:(e,t,o)=>{"use strict";o.d(t,{yM:()=>i});var r=o(4922),s=o.n(r),n=o(5114);o(3044);const c=s()("wc-admin:tracks");function i(e,t){return c("recordevent %s %o","wcadmin_"+e,t,{_tqk:window._tkq,shouldRecord:!(n.D||!window._tkq||!window.wcTracks||!window.wcTracks.isEnabled)}),!(!window.wcTracks||"function"!=typeof window.wcTracks.recordEvent)&&(n.D?(window.wcTracks.validateEvent(e,t),!1):void window.wcTracks.recordEvent(e,t))}},3044:(e,t,o)=>{"use strict";var r=o(4922);o.n(r)()("wc-admin:tracks:stats")},5114:(e,t,o)=>{"use strict";o.d(t,{D:()=>r});const r=!1},6921:(e,t,o)=>{"use strict";o.d(t,{A:()=>s});var r=o(7143);const s=({blockClientId:e})=>{const{isDescendentOfSingleProductBlock:t}=(0,r.useSelect)((t=>{const{getBlockParentsByBlockName:o}=t("core/block-editor");return{isDescendentOfSingleProductBlock:o(null==e?void 0:e.replace("block-",""),["woocommerce/single-product"]).length>0}}),[e]);return{isDescendentOfSingleProductBlock:t}}},7157:(e,t,o)=>{"use strict";o.d(t,{W:()=>l});var r=o(2294),s=o(4997),n=o(7143),c=o(3993);class i{constructor(){(0,r.A)(this,"blocks",new Map),(0,r.A)(this,"currentTemplateId",void 0),(0,r.A)(this,"initialized",!1),(0,r.A)(this,"attemptedRegisteredBlocks",new Set),this.initializeSubscriptions()}static getInstance(){return i.instance||(i.instance=new i),i.instance}parseTemplateId(e){const t=(0,c.isNumber)(e)?void 0:e;return null==t?void 0:t.split("//")[1]}initializeSubscriptions(){if(this.initialized)return;const e=(0,n.subscribe)((()=>{const t=(0,n.select)("core/edit-site"),o=(0,n.select)("core/edit-post");if(t||o)if(t){const o=t.getEditedPostId();e(),this.currentTemplateId="string"==typeof o?this.parseTemplateId(o):void 0,(0,n.subscribe)((()=>{const e=this.currentTemplateId;this.currentTemplateId=this.parseTemplateId(t.getEditedPostId()),e!==this.currentTemplateId&&this.handleTemplateChange(e)}),"core/edit-site"),this.initialized=!0}else o&&(e(),this.blocks.forEach((e=>{if(e.isAvailableOnPostEditor){const t=e.variationName||e.blockName;this.hasAttemptedRegistration(t)||this.registerBlock(e)}})),this.initialized=!0)}))}handleTemplateChange(e){var t;((null===(t=this.currentTemplateId)||void 0===t?void 0:t.includes("single-product"))||(null==e?void 0:e.includes("single-product")))&&this.blocks.forEach((e=>{this.unregisterBlock(e),this.registerBlock(e)}))}hasAttemptedRegistration(e){return this.attemptedRegisteredBlocks.has(e)}unregisterBlock(e){const{blockName:t,isVariationBlock:o,variationName:r}=e;try{o&&r?((0,s.unregisterBlockVariation)(t,r),this.attemptedRegisteredBlocks.delete(r)):((0,s.unregisterBlockType)(t),this.attemptedRegisteredBlocks.delete(t))}catch(e){console.debug(`Failed to unregister block ${t}:`,e)}}registerBlock(e){const{blockName:t,settings:o,isVariationBlock:r,variationName:i,isAvailableOnPostEditor:l}=e;try{const e=i||t;if(this.hasAttemptedRegistration(e))return;const d=(0,n.select)("core/edit-site");if(!d&&!l)return;if(r)(0,s.registerBlockVariation)(t,o);else{var a;const e=(0,c.isEmpty)(null==o?void 0:o.ancestor)?["woocommerce/single-product"]:null==o?void 0:o.ancestor,r=d&&(null===(a=this.currentTemplateId)||void 0===a?void 0:a.includes("single-product"));(0,s.registerBlockType)(t,{...o,ancestor:r?void 0:e})}this.attemptedRegisteredBlocks.add(e)}catch(e){console.error(`Failed to register block ${t}:`,e)}}registerBlockConfig(e){const t=e.variationName||e.blockName;this.blocks.set(t,e),this.registerBlock(e)}}(0,r.A)(i,"instance",void 0);const l=(e,t)=>{const o="string"==typeof e?e:e.name;if(!o)return void console.error("registerProductBlockType: Block name is required for registration");const r="string"==typeof e?{}:(({name:e,...t})=>t)(e),{isVariationBlock:s,variationName:n,isAvailableOnPostEditor:c,...l}={...r,...t||{}},a={blockName:o,settings:{...l},isVariationBlock:null!=s&&s,variationName:null!=n?n:void 0,isAvailableOnPostEditor:null!=c&&c};i.getInstance().registerBlockConfig(a)}},4154:(e,t,o)=>{"use strict";o.d(t,{A:()=>g});var r=o(7143);const s="woocommerce/product-type-template-state",n="SWITCH_PRODUCT_TYPE",c="SET_PRODUCT_TYPES",i="REGISTER_LISTENER",l="UNREGISTER_LISTENER",a=(0,o(5703).getSetting)("productTypes",{});var d;const u=Object.keys(a).map((e=>({slug:e,label:a[e]}))),p={productTypes:{list:u,current:null===(d=u[0])||void 0===d?void 0:d.slug},listeners:[]},m={switchProductType:e=>({type:n,current:e}),setProductTypes:e=>({type:c,productTypes:e}),registerListener:e=>({type:i,listener:e}),unregisterListener:e=>({type:l,listener:e})},w=(0,r.createReduxStore)(s,{reducer:(e=p,t)=>{switch(t.type){case c:return{...e,productTypes:{...e.productTypes,list:t.productTypes||[]}};case n:return{...e,productTypes:{...e.productTypes,current:t.current}};case i:return{...e,listeners:[...e.listeners,t.listener||""]};case l:return{...e,listeners:e.listeners.filter((e=>e!==t.listener))};default:return e}},actions:m,selectors:{getProductTypes:e=>e.productTypes.list,getCurrentProductType:e=>e.productTypes.list.find((t=>t.slug===e.productTypes.current)),getRegisteredListeners:e=>e.listeners}});function g(){const{productTypes:e,current:t,registeredListeners:o}=(0,r.useSelect)((e=>{const{getProductTypes:t,getCurrentProductType:o,getRegisteredListeners:r}=e(w);return{productTypes:t(),current:o(),registeredListeners:r()}}),[]),{switchProductType:s,registerListener:n,unregisterListener:c}=(0,r.useDispatch)(w);return{productTypes:e,current:t,set:s,registeredListeners:o,registerListener:n,unregisterListener:c}}(0,r.select)(s)||(0,r.register)(w)},8138:(e,t,o)=>{"use strict";o.d(t,{shouldBlockifiedAddToCartWithOptionsBeRegistered:()=>N});var r=o(498);const s=window.wp.plugins;var n=o(5703),c=o(3993),i=o(7157),l=o(1609),a=o(7723),d=o(7143),u=o(6427),p=o(6911);const m=window.wp.editor;var w=o(4154);function g(){const{productTypes:e,current:t,set:o}=(0,w.A)();return(0,l.createElement)(u.SelectControl,{label:(0,a.__)("Type switcher","woocommerce"),value:null==t?void 0:t.slug,options:e.map((e=>({label:e.label,value:e.slug}))),onChange:e=>{o(e),(0,p.yM)("blocks_add_to_cart_with_options_product_type_switched",{context:"inspector",from:null==t?void 0:t.slug,to:e})},help:(0,a.__)("Switch product type to see how the template adapts to each one.","woocommerce")})}const v=JSON.parse('{"name":"woocommerce/add-to-cart-with-options","version":"1.0.0","title":"Add to Cart with Options (Experimental)","description":"Create an \\"Add To Cart\\" composition by using blocks","category":"woocommerce-product-elements","attributes":{"isDescendentOfSingleProductBlock":{"type":"boolean","default":false}},"usesContext":["postId"],"textdomain":"woocommerce","supports":{"interactivity":true},"apiVersion":3,"$schema":"https://schemas.wp.org/trunk/block.json"}');var k=o(6087),b=o(4715),h=o(6921);const f=({features:e})=>{const{isBlockifiedAddToCart:t}=e;if(!t)return null;const o=Object.keys(e).filter((t=>e[t]));return(0,l.createElement)(b.InspectorControls,null,(0,l.createElement)(u.PanelBody,{title:"Development"},(0,l.createElement)(u.Flex,{gap:3,direction:"column"},(0,l.createElement)(u.Notice,{status:"warning",isDismissible:!1},(0,a.__)("Development features enabled.","woocommerce")),o.map((e=>(0,l.createElement)(u.FlexItem,{key:e},e))))))};var y=o(5573);const _=(0,l.createElement)(y.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,l.createElement)("path",{fill:"none",d:"M0 0h24v24H0V0z"}),(0,l.createElement)("path",{d:"M12 6a9.77 9.77 0 0 1 8.82 5.5C19.17 14.87 15.79 17 12 17s-7.17-2.13-8.82-5.5A9.77 9.77 0 0 1 12 6m0-2C7 4 2.73 7.11 1 11.5 2.73 15.89 7 19 12 19s9.27-3.11 11-7.5C21.27 7.11 17 4 12 4zm0 5a2.5 2.5 0 0 1 0 5 2.5 2.5 0 0 1 0-5m0-2c-2.48 0-4.5 2.02-4.5 4.5S9.52 16 12 16s4.5-2.02 4.5-4.5S14.48 7 12 7z"}));var E=o(2796);function T(){const{current:e,productTypes:t,set:o}=(0,w.A)(),{product:r}=(0,E.useProductDataContext)();return null!=r&&r.id||(null==t?void 0:t.length)<2?null:(0,l.createElement)(u.ToolbarGroup,null,(0,l.createElement)(u.ToolbarDropdownMenu,{icon:(0,l.createElement)(u.Icon,{icon:_}),text:(null==e?void 0:e.label)||(0,a.__)("Switch product type","woocommerce"),value:null==e?void 0:e.slug,controls:t.map((t=>({title:t.label,onClick:()=>{o(t.slug),(null==e?void 0:e.slug)!==t.slug&&(0,p.yM)("blocks_add_to_cart_with_options_product_type_switched",{context:"toolbar",from:null==e?void 0:e.slug,to:t.slug})}})))}))}var B=o(4997),C=o(4187),S=o(5050);const P=({blockClientId:e})=>{const t=(0,a.__)("Switch back to the classic Add to Cart with Options block.","woocommerce"),o=(0,a.__)("Switch back","woocommerce");return(0,l.createElement)(C.u,{isDismissible:!1,actionLabel:o,onActionClick:async()=>{const t=await(e=>{var t;const o=(0,d.select)("core/block-editor").getBlocks(),r=(0,S.P)({blocks:o,findCondition:t=>t.name===v.name&&t.clientId===e});if(!r)return!1;const s=(0,S.P)({blocks:o,findCondition:e=>"woocommerce/add-to-cart-with-options-quantity-selector"===e.name}),n=(0,B.createBlock)("woocommerce/add-to-cart-form",{quantitySelectorStyle:(null==s||null===(t=s.attributes)||void 0===t?void 0:t.quantitySelectorStyle)||"input"});return(0,d.dispatch)("core/block-editor").replaceBlock(r.clientId,n),!0})(e);t&&(0,p.yM)("blocks_add_to_cart_with_options_migration",{transform_to:"legacy"})}},t)};var O=o(3605);const x=e=>{const{setAttributes:t}=e,o=(0,b.useBlockProps)(),r=null==o?void 0:o.id,{isDescendentOfSingleProductBlock:s}=(0,h.A)({blockClientId:r}),{registerListener:n,unregisterListener:c}=(0,w.A)();(0,k.useEffect)((()=>(t({isDescendentOfSingleProductBlock:s}),n(r),()=>{c(r)})),[t,s,r,n,c]);const i=(0,O.A)();return(0,l.createElement)(l.Fragment,null,(0,l.createElement)(b.InspectorControls,null,(0,l.createElement)(P,{blockClientId:null==e?void 0:e.clientId})),(0,l.createElement)(b.BlockControls,null,(0,l.createElement)(T,null)),(0,l.createElement)(f,{features:{isBlockifiedAddToCart:!0}}),(0,l.createElement)("div",{...o},(0,l.createElement)(b.InnerBlocks,{template:i})))},A=()=>{const e=b.useBlockProps.save(),t=b.useInnerBlocksProps.save(e);return(0,l.createElement)("div",{...t})};o(4690);const I=(0,n.getSettingWithCoercion)("isBlockifiedAddToCart",!1,c.isBoolean),N=(()=>{const{experimentalBlocksEnabled:e}=(0,n.getSetting)("wcBlocksConfig",{experimentalBlocksEnabled:!1});return e})()&&I;if(N){const e="document-settings-template-selector-pane";(0,s.getPlugin)(e)||(0,s.registerPlugin)(e,{render:function(){const{slug:e,type:t}=(0,d.useSelect)((e=>{const{slug:t,type:o}=e("core/editor").getCurrentPost();return{slug:t,type:o}}),[]),{registeredListeners:o}=(0,w.A)();return"wp_template"===t&&"single-product"===e&&o.length>0?(0,l.createElement)(m.PluginDocumentSettingPanel,{name:"woocommerce/product-type-selector",title:(0,a.__)("Product Type","woocommerce")},(0,l.createElement)(g,null)):null}}),(0,i.W)({...v,icon:{src:r.A},edit:x,save:A,ancestor:["woocommerce/single-product"]},{isAvailableOnPostEditor:!0})}},3605:(e,t,o)=>{"use strict";o.d(t,{A:()=>s});var r=o(7723);const s=(e="input")=>[["core/heading",{level:2,content:(0,r.__)("Add to Cart","woocommerce")}],["woocommerce/add-to-cart-with-options-variation-selector"],["woocommerce/product-stock-indicator"],["woocommerce/add-to-cart-with-options-quantity-selector",{quantitySelectorStyle:e}],["woocommerce/product-button",{textAlign:"center",fontSize:"small"}]]},7503:(e,t,o)=>{"use strict";o.r(t);var r=o(1609),s=o(4997),n=o(7104),c=o(498);const i=JSON.parse('{"name":"woocommerce/add-to-cart-with-options-variation-selector","version":"1.0.0","title":"Variation Selector (Experimental)","description":"Display a dropdown to select a variation to add to cart.","category":"woocommerce-product-elements","keywords":["WooCommerce"],"usesContext":["postId"],"ancestor":["woocommerce/add-to-cart-with-options"],"textdomain":"woocommerce","apiVersion":3,"$schema":"https://schemas.wp.org/trunk/block.json"}');var l=o(7723),a=o(4715),d=o(2796),u=o(6427),p=o(4154);var m=o(8138);o(7754),o(3096),m.shouldBlockifiedAddToCartWithOptionsBeRegistered&&(0,s.registerBlockType)(i,{edit:e=>{const{className:t}=e.attributes,{product:o}=(0,d.useProductDataContext)(),{current:s}=(0,p.A)(),n=(0,a.useBlockProps)({className:t});if("variable"!==(null==s?void 0:s.slug))return null;const c=0===o.id;return(0,r.createElement)(r.Fragment,null,(0,r.createElement)("div",{...n},(0,r.createElement)("div",{className:"wc-block-variation-selector"},c?[(0,l.__)("Color","woocommerce"),(0,l.__)("Size","woocommerce")].map((e=>(0,r.createElement)("div",{className:"wc-block-variation-selector__wrapper",key:e},(0,r.createElement)(u.__experimentalHeading,{className:"wc-block-variation-selector__label",level:"3"},e),(0,r.createElement)(u.SelectControl,{value:"",options:[{label:(0,l.__)("Choose an option","woocommerce"),value:"",disabled:!0}],disabled:!0,onChange:()=>{},className:"wc-block-variation-selector__select"})))):Object.entries(o.attributes).map((([e,t])=>(0,r.createElement)("div",{className:"wc-block-variation-selector__wrapper",key:e},(0,r.createElement)(u.__experimentalHeading,{className:"wc-block-variation-selector__label",level:"3"},t.name),(0,r.createElement)(u.SelectControl,{id:`pa_${t.taxonomy}`,value:"",options:[{label:(0,l.__)("Choose an option","woocommerce"),value:"",disabled:!0}],disabled:!0,onChange:()=>{},className:"wc-block-variation-selector__select"})))))))},attributes:i.attributes,icon:{src:(0,r.createElement)(n.A,{icon:c.A,className:"wc-block-editor-components-block-icon"})},save:()=>null})},4187:(e,t,o)=>{"use strict";o.d(t,{u:()=>c});var r=o(1609),s=o(6427),n=o(851);function c({children:e,className:t,actionLabel:o,onActionClick:c,...i}){return(0,r.createElement)(s.Notice,{...i,className:(0,n.$)("wc-block-editor-components-upgrade-downgrade-notice",t),actions:[{label:o,onClick:c,noDefaultClasses:!0,variant:"link"}]},(0,r.createElement)("div",{className:"wc-block-editor-components-upgrade-downgrade-notice__text"},e))}o(2615)},5050:(e,t,o)=>{"use strict";o.d(t,{P:()=>r});const r=({blocks:e,findCondition:t})=>{for(const o of e){if(t(o))return o;if(o.innerBlocks){const e=r({blocks:o.innerBlocks,findCondition:t});if(e)return e}}}},4690:()=>{},3096:()=>{},7754:()=>{},2615:()=>{},1609:e=>{"use strict";e.exports=window.React},2796:e=>{"use strict";e.exports=window.wc.wcBlocksSharedContext},5703:e=>{"use strict";e.exports=window.wc.wcSettings},3993:e=>{"use strict";e.exports=window.wc.wcTypes},4715:e=>{"use strict";e.exports=window.wp.blockEditor},4997:e=>{"use strict";e.exports=window.wp.blocks},6427:e=>{"use strict";e.exports=window.wp.components},7143:e=>{"use strict";e.exports=window.wp.data},6087:e=>{"use strict";e.exports=window.wp.element},7723:e=>{"use strict";e.exports=window.wp.i18n},5573:e=>{"use strict";e.exports=window.wp.primitives}},s={};function n(e){var t=s[e];if(void 0!==t)return t.exports;var o=s[e]={exports:{}};return r[e].call(o.exports,o,o.exports,n),o.exports}n.m=r,e=[],n.O=(t,o,r,s)=>{if(!o){var c=1/0;for(d=0;d<e.length;d++){for(var[o,r,s]=e[d],i=!0,l=0;l<o.length;l++)(!1&s||c>=s)&&Object.keys(n.O).every((e=>n.O[e](o[l])))?o.splice(l--,1):(i=!1,s<c&&(c=s));if(i){e.splice(d--,1);var a=r();void 0!==a&&(t=a)}}return t}s=s||0;for(var d=e.length;d>0&&e[d-1][2]>s;d--)e[d]=e[d-1];e[d]=[o,r,s]},n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},o=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,n.t=function(e,r){if(1&r&&(e=this(e)),8&r)return e;if("object"==typeof e&&e){if(4&r&&e.__esModule)return e;if(16&r&&"function"==typeof e.then)return e}var s=Object.create(null);n.r(s);var c={};t=t||[null,o({}),o([]),o(o)];for(var i=2&r&&e;"object"==typeof i&&!~t.indexOf(i);i=o(i))Object.getOwnPropertyNames(i).forEach((t=>c[t]=()=>e[t]));return c.default=()=>e,n.d(s,c),s},n.d=(e,t)=>{for(var o in t)n.o(t,o)&&!n.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.j=2475,(()=>{var e={2475:0,7435:0};n.O.j=t=>0===e[t];var t=(t,o)=>{var r,s,[c,i,l]=o,a=0;if(c.some((t=>0!==e[t]))){for(r in i)n.o(i,r)&&(n.m[r]=i[r]);if(l)var d=l(n)}for(t&&t(o);a<c.length;a++)s=c[a],n.o(e,s)&&e[s]&&e[s][0](),e[s]=0;return n.O(d)},o=self.webpackChunkwebpackWcBlocksMainJsonp=self.webpackChunkwebpackWcBlocksMainJsonp||[];o.forEach(t.bind(null,0)),o.push=t.bind(null,o.push.bind(o))})();var c=n.O(void 0,[94],(()=>n(7503)));c=n.O(c),((this.wc=this.wc||{}).blocks=this.wc.blocks||{})["add-to-cart-with-options-variation-selector"]=c})();