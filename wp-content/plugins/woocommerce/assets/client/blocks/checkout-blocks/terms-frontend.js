"use strict";(self.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=self.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[4654],{8568:(e,t,o)=>{o.d(t,{VM:()=>l,Xm:()=>n,iG:()=>s});var c=o(1609),a=o(1e3),r=o(8645);const n=()=>{const{extensions:e,receiveCart:t,...o}=(0,r.V)(),n={extensions:e,cart:o,context:"woocommerce/checkout"};return(0,c.createElement)(a.ExperimentalOrderMeta.Slot,{...n})},{Fill:s,Slot:l}=(0,a.createSlotFill)("checkoutOrderSummaryActionArea")},9226:(e,t,o)=>{o.r(t),o.d(t,{default:()=>b});var c=o(1609),a=o(7723),r=o(851),n=o(6087),s=o(4656),l=o(2721),i=o(9491),m=o(7143),d=o(7594),u=o(2516);const h=u.gu?`<a href="${u.gu}" target="_blank">${(0,a.__)("Terms and Conditions","woocommerce")}</a>`:(0,a.__)("Terms and Conditions","woocommerce"),_=u.pk?`<a href="${u.pk}" target="_blank">${(0,a.__)("Privacy Policy","woocommerce")}</a>`:(0,a.__)("Privacy Policy","woocommerce"),k=(0,a.sprintf)(/* translators: %1$s terms page link, %2$s privacy page link. */ /* translators: %1$s terms page link, %2$s privacy page link. */
(0,a.__)("By proceeding with your purchase you agree to our %1$s and %2$s","woocommerce"),h,_),p=(0,a.sprintf)(/* translators: %1$s terms page link, %2$s privacy page link. */ /* translators: %1$s terms page link, %2$s privacy page link. */
(0,a.__)("You must accept our %1$s and %2$s to continue with your purchase.","woocommerce"),h,_);var w=o(8568);const b=(0,i.withInstanceId)((({text:e,checkbox:t,instanceId:o,className:i,showSeparator:u})=>{const[h,_]=(0,n.useState)(!1),{isDisabled:b}=(0,l.w)(),g="terms-and-conditions-"+o,{setValidationErrors:E,clearValidationError:C}=(0,m.useDispatch)(d.validationStore),f=(0,m.useSelect)((e=>e(d.validationStore).getValidationError(g))),y=!(null==f||!f.message||null!=f&&f.hidden);return(0,n.useEffect)((()=>{if(t)return h?C(g):E({[g]:{message:(0,a.__)("Please read and accept the terms and conditions.","woocommerce"),hidden:!0}}),()=>{C(g)}}),[t,h,g,C,E]),(0,c.createElement)(c.Fragment,null,(0,c.createElement)(w.VM,null),(0,c.createElement)("div",{className:(0,r.A)("wc-block-checkout__terms",{"wc-block-checkout__terms--disabled":b,"wc-block-checkout__terms--with-separator":"false"!==u&&!1!==u},i)},t?(0,c.createElement)(c.Fragment,null,(0,c.createElement)(s.CheckboxControl,{id:"terms-and-conditions",checked:h,onChange:()=>_((e=>!e)),hasError:y,disabled:b},(0,c.createElement)("span",{className:"wc-block-components-checkbox__label",dangerouslySetInnerHTML:{__html:e||p}}))):(0,c.createElement)("span",{className:"wc-block-components-checkbox__label",dangerouslySetInnerHTML:{__html:e||k}})))}))}}]);