jQuery(function(e){window.wcTracks=window.wcTracks||{},window.wcTracks.recordEvent=window.wcTracks.recordEvent||function(){};var o={states:null,init:function(){"undefined"!=typeof woocommerce_admin_meta_boxes_order&&"undefined"!=typeof woocommerce_admin_meta_boxes_order.countries&&(this.states=JSON.parse(woocommerce_admin_meta_boxes_order.countries.replace(/&quot;/g,'"'))),e(".js_field-country").selectWoo().on("change",this.change_country),e(".js_field-country").trigger("change",[!0]),e(document.body).on("change","select.js_field-state",this.change_state),e("#woocommerce-order-actions input, #woocommerce-order-actions a").on("click",function(){window.onbeforeunload=""}),e("a.edit_address").on("click",this.edit_address),e("a.billing-same-as-shipping").on("click",this.copy_billing_to_shipping),e("a.load_customer_billing").on("click",this.load_billing),e("a.load_customer_shipping").on("click",this.load_shipping),e("#customer_user").on("change",this.change_customer_user)},change_country:function(t,r){if(void 0===r&&(r=!1),null!==o.states){var a,i=e(this),n=i.val(),d=i.parents("div.edit_address").find(":input.js_field-state"),c=d.parent(),_=d.val(),m=d.attr("name"),s=d.attr("id"),l=i.data("woocommerce.stickState-"+n)?i.data("woocommerce.stickState-"+n):_,u=d.attr("placeholder");if(r&&i.data("woocommerce.stickState-"+n,l),c.show().find(".select2-container").remove(),e.isEmptyObject(o.states[n]))a=e('<input type="text" />').prop("id",s).prop("name",m).prop("placeholder",u).addClass("js_field-state").val(_),d.replaceWith(a);else{var p=o.states[n],w=e('<option value=""></option>').text(woocommerce_admin_meta_boxes_order.i18n_select_state_text);a=e("<select></select>").prop("id",s).prop("name",m).prop("placeholder",u).addClass("js_field-state select short").append(w),e.each(p,function(o){var t=e("<option></option>").prop("value",o).text(p[o]);o===_&&t.prop("selected"),a.append(t)}),a.val(l),d.replaceWith(a),a.show().selectWoo().hide().trigger("change")}e(document.body).trigger("contry-change.woocommerce",[n,e(this).closest("div")]),e(document.body).trigger("country-change.woocommerce",[n,e(this).closest("div")])}},change_state:function(){var o=e(this),t=o.val(),r=o.parents("div.edit_address").find(":input.js_field-country"),a=r.val();r.data("woocommerce.stickState-"+a,t)},init_tiptip:function(){e("#tiptip_holder").removeAttr("style"),e("#tiptip_arrow").removeAttr("style"),e(".tips").tipTip({attribute:"data-tip",fadeIn:50,fadeOut:50,delay:200,keepAlive:!0})},edit_address:function(o){o.preventDefault();var t=e(this),r=t.closest(".order_data_column"),a=r.find("div.edit_address"),i=r.find("div.address"),n=a.find(".js_field-country"),d=a.find(".js_field-state"),c=Boolean(a.find('input[name^="_billing_"]').length);i.hide(),t.parent().find("a").toggle(),n.val()||(n.val(woocommerce_admin_meta_boxes_order.default_country).trigger("change"),d.val(woocommerce_admin_meta_boxes_order.default_state).trigger("change")),a.show();var _=c?"order_edit_billing_address_click":"order_edit_shipping_address_click";window.wcTracks.recordEvent(_,{order_id:woocommerce_admin_meta_boxes.post_id,status:e("#order_status").val()})},change_customer_user:function(){e("#_billing_country").val()||(e("a.edit_address").trigger("click"),o.load_billing(!0),o.load_shipping(!0))},load_billing:function(o){if(!0===o||window.confirm(woocommerce_admin_meta_boxes.load_billing)){var t=e("#customer_user").val();if(!t)return window.alert(woocommerce_admin_meta_boxes.no_customer_selected),!1;var r={user_id:t,action:"woocommerce_get_customer_details",security:woocommerce_admin_meta_boxes.get_customer_details_nonce};e(this).closest("div.edit_address").block({message:null,overlayCSS:{background:"#fff",opacity:.6}}),e.ajax({url:woocommerce_admin_meta_boxes.ajax_url,data:r,type:"POST",success:function(o){o&&o.billing&&e.each(o.billing,function(o,t){e(":input#_billing_"+o).val(t).trigger("change")}),e("div.edit_address").unblock()}})}return!1},load_shipping:function(o){if(!0===o||window.confirm(woocommerce_admin_meta_boxes.load_shipping)){var t=e("#customer_user").val();if(!t)return window.alert(woocommerce_admin_meta_boxes.no_customer_selected),!1;var r={user_id:t,action:"woocommerce_get_customer_details",security:woocommerce_admin_meta_boxes.get_customer_details_nonce};e(this).closest("div.edit_address").block({message:null,overlayCSS:{background:"#fff",opacity:.6}}),e.ajax({url:woocommerce_admin_meta_boxes.ajax_url,data:r,type:"POST",success:function(o){o&&o.billing&&e.each(o.shipping,function(o,t){e(":input#_shipping_"+o).val(t).trigger("change")}),e("div.edit_address").unblock()}})}return!1},copy_billing_to_shipping:function(){return window.confirm(woocommerce_admin_meta_boxes.copy_billing)&&e('.order_data_column :input[name^="_billing_"]').each(function(){var o=e(this).attr("name");o=o.replace("_billing_","_shipping_"),e(":input#"+o).val(e(this).val()).trigger("change")}),!1}},t={init:function(){this.stupidtable.init(),e("#woocommerce-order-items").on("click","button.add-line-item",this.add_line_item).on("click","button.add-coupon",this.add_coupon).on("click","a.remove-coupon",this.remove_coupon).on("click","button.refund-items",this.refund_items).on("click",".cancel-action",this.cancel).on("click",".refund-actions .cancel-action",this.track_cancel).on("click","button.add-order-item",this.add_item).on("click","button.add-order-fee",this.add_fee).on("click","button.add-order-shipping",this.add_shipping).on("click","button.add-order-tax",this.add_tax).on("click","button.save-action",this.save_line_items).on("click","a.delete-order-tax",this.delete_tax).on("click","button.calculate-action",this.recalculate).on("click","a.edit-order-item",this.edit_item).on("click","a.delete-order-item",this.delete_item).on("click",".delete_refund",this.refunds.delete_refund).on("click","button.do-api-refund, button.do-manual-refund",this.refunds.do_refund).on("change",".refund input.refund_line_total, .refund input.refund_line_tax",this.refunds.input_changed).on("change keyup",".wc-order-refund-items #refund_amount",this.refunds.amount_changed).on("change","input.refund_order_item_qty",this.refunds.refund_quantity_changed).on("change","input.quantity",this.quantity_changed).on("keyup change",".split-input :input",function(){var o=e(this).parent().prev().find(":input");o&&(""===o.val()||o.is(".match-total"))&&o.val(e(this).val()).addClass("match-total")}).on("keyup",".split-input :input",function(){e(this).removeClass("match-total")}).on("click","button.add_order_item_meta",this.item_meta.add).on("click","button.remove_order_item_meta",this.item_meta.remove).on("wc_order_items_reload",this.reload_items).on("wc_order_items_reloaded",this.reloaded_items),e(document.body).on("wc_backbone_modal_loaded",this.backbone.init).on("wc_backbone_modal_response",this.backbone.response)},block:function(){e("#woocommerce-order-items").block({message:null,overlayCSS:{background:"#fff",opacity:.6}})},unblock:function(){e("#woocommerce-order-items").unblock()},filter_data:function(o,t){const r=e("#woocommerce-order-items").triggerHandler(`woocommerce_order_meta_box_${o}_ajax_data`,[t]);return r||t},reload_items:function(){var o={order_id:woocommerce_admin_meta_boxes.post_id,action:"woocommerce_load_order_items",security:woocommerce_admin_meta_boxes.order_item_nonce};o=t.filter_data("reload_items",o),t.block(),e.ajax({url:woocommerce_admin_meta_boxes.ajax_url,data:o,type:"POST",success:function(o){e("#woocommerce-order-items").find(".inside").empty(),e("#woocommerce-order-items").find(".inside").append(o),t.reloaded_items(),t.unblock()}})},reloaded_items:function(){o.init_tiptip(),t.stupidtable.init()},quantity_changed:function(){var o=e(this).closest("tr.item"),t=e(this).val(),r=e(this).attr("data-qty"),a=e("input.line_total",o),i=e("input.line_subtotal",o),n=accounting.unformat(a.attr("data-total"),woocommerce_admin.mon_decimal_point)/r;a.val(parseFloat(accounting.formatNumber(n*t,woocommerce_admin_meta_boxes.rounding_precision,"")).toString().replace(".",woocommerce_admin.mon_decimal_point));var d=accounting.unformat(i.attr("data-subtotal"),woocommerce_admin.mon_decimal_point)/r;i.val(parseFloat(accounting.formatNumber(d*t,woocommerce_admin_meta_boxes.rounding_precision,"")).toString().replace(".",woocommerce_admin.mon_decimal_point)),e("input.line_tax",o).each(function(){var a=e(this),i=a.data("tax_id"),n=accounting.unformat(a.attr("data-total_tax"),woocommerce_admin.mon_decimal_point)/r,d=e('input.line_subtotal_tax[data-tax_id="'+i+'"]',o),c=accounting.unformat(d.attr("data-subtotal_tax"),woocommerce_admin.mon_decimal_point)/r,_="yes"===woocommerce_admin_meta_boxes.round_at_subtotal,m=woocommerce_admin_meta_boxes[_?"rounding_precision":"currency_format_num_decimals"];0<n&&a.val(parseFloat(accounting.formatNumber(n*t,m,"")).toString().replace(".",woocommerce_admin.mon_decimal_point)),0<c&&d.val(parseFloat(accounting.formatNumber(c*t,m,"")).toString().replace(".",woocommerce_admin.mon_decimal_point))}),e(this).trigger("quantity_changed")},add_line_item:function(){return e("div.wc-order-add-item").slideDown(),e("div.wc-order-data-row-toggle").not("div.wc-order-add-item").slideUp(),window.wcTracks.recordEvent("order_edit_add_items_click",{order_id:woocommerce_admin_meta_boxes.post_id,status:e("#order_status").val()}),!1},add_coupon:function(){window.wcTracks.recordEvent("order_edit_add_coupon_click",{order_id:woocommerce_admin_meta_boxes.post_id,status:e("#order_status").val()});var o=window.prompt(woocommerce_admin_meta_boxes.i18n_apply_coupon);if(null==o)window.wcTracks.recordEvent("order_edit_add_coupon_cancel",{order_id:woocommerce_admin_meta_boxes.post_id,status:e("#order_status").val()});else{t.block();var r=e("#customer_user").val(),a=e("#_billing_email").val(),i=e.extend({},t.get_taxable_address(),{action:"woocommerce_add_coupon_discount",dataType:"json",order_id:woocommerce_admin_meta_boxes.post_id,security:woocommerce_admin_meta_boxes.order_item_nonce,coupon:o,user_id:r,user_email:a});i=t.filter_data("add_coupon",i),e.ajax({url:woocommerce_admin_meta_boxes.ajax_url,data:i,type:"POST",success:function(o){o.success?(e("#woocommerce-order-items").find(".inside").empty(),e("#woocommerce-order-items").find(".inside").append(o.data.html),o.data.notes_html&&(e("ul.order_notes").empty(),e("ul.order_notes").append(e(o.data.notes_html).find("li"))),t.reloaded_items(),t.unblock()):window.alert(o.data.error),t.unblock()},complete:function(){window.wcTracks.recordEvent("order_edit_added_coupon",{order_id:i.order_id,status:e("#order_status").val()})}})}return!1},remove_coupon:function(){var o=e(this);t.block();var r=e.extend({},t.get_taxable_address(),{action:"woocommerce_remove_order_coupon",dataType:"json",order_id:woocommerce_admin_meta_boxes.post_id,security:woocommerce_admin_meta_boxes.order_item_nonce,coupon:o.data("code")});r=t.filter_data("remove_coupon",r),e.post(woocommerce_admin_meta_boxes.ajax_url,r,function(o){o.success?(e("#woocommerce-order-items").find(".inside").empty(),e("#woocommerce-order-items").find(".inside").append(o.data.html),o.data.notes_html&&(e("ul.order_notes").empty(),e("ul.order_notes").append(e(o.data.notes_html).find("li"))),t.reloaded_items(),t.unblock()):window.alert(o.data.error),t.unblock()})},refund_items:function(){return e("div.wc-order-refund-items").slideDown(),e("div.wc-order-data-row-toggle").not("div.wc-order-refund-items").slideUp(),e("div.wc-order-totals-items").slideUp(),e("#woocommerce-order-items").find("div.refund").show(),e(".wc-order-edit-line-item .wc-order-edit-line-item-actions").hide(),window.wcTracks.recordEvent("order_edit_refund_button_click",{order_id:woocommerce_admin_meta_boxes.post_id,status:e("#order_status").val()}),!1},cancel:function(){return e("div.wc-order-data-row-toggle").not("div.wc-order-bulk-actions").slideUp(),e("div.wc-order-bulk-actions").slideDown(),e("div.wc-order-totals-items").slideDown(),e("#woocommerce-order-items").find("div.refund").hide(),e(".wc-order-edit-line-item .wc-order-edit-line-item-actions").show(),"true"===e(this).attr("data-reload")&&t.reload_items(),window.wcTracks.recordEvent("order_edit_add_items_cancelled",{order_id:woocommerce_admin_meta_boxes.post_id,status:e("#order_status").val()}),!1},track_cancel:function(){window.wcTracks.recordEvent("order_edit_refund_cancel",{order_id:woocommerce_admin_meta_boxes.post_id,status:e("#order_status").val()})},add_item:function(){return e(this).WCBackboneModal({template:"wc-modal-add-products"}),!1},add_fee:function(){window.wcTracks.recordEvent("order_edit_add_fee_click",{order_id:woocommerce_admin_meta_boxes.post_id,status:e("#order_status").val()});var r=window.prompt(woocommerce_admin_meta_boxes.i18n_add_fee);if(null==r)window.wcTracks.recordEvent("order_edit_add_fee_cancel",{order_id:woocommerce_admin_meta_boxes.post_id,status:e("#order_status").val()});else{t.block();var a=e.extend({},t.get_taxable_address(),{action:"woocommerce_add_order_fee",dataType:"json",order_id:woocommerce_admin_meta_boxes.post_id,security:woocommerce_admin_meta_boxes.order_item_nonce,amount:r});a=t.filter_data("add_fee",a),e.post(woocommerce_admin_meta_boxes.ajax_url,a,function(r){r.success?(e("#woocommerce-order-items").find(".inside").empty(),e("#woocommerce-order-items").find(".inside").append(r.data.html),t.reloaded_items(),t.unblock(),window.wcTracks.recordEvent("order_edit_added_fee",{order_id:woocommerce_admin_meta_boxes.post_id,status:e("#order_status").val()})):window.alert(r.data.error),o.init_tiptip(),t.unblock()})}return!1},add_shipping:function(){t.block();var r={action:"woocommerce_add_order_shipping",order_id:woocommerce_admin_meta_boxes.post_id,security:woocommerce_admin_meta_boxes.order_item_nonce,dataType:"json"};return r=t.filter_data("add_shipping",r),e.post(woocommerce_admin_meta_boxes.ajax_url,r,function(r){r.success?(e("table.woocommerce_order_items tbody#order_shipping_line_items").append(r.data.html),window.wcTracks.recordEvent("order_edit_add_shipping",{order_id:woocommerce_admin_meta_boxes.post_id,status:e("#order_status").val()})):window.alert(r.data.error),o.init_tiptip(),t.unblock()}),!1},add_tax:function(){return e(this).WCBackboneModal({template:"wc-modal-add-tax"}),!1},edit_item:function(){return e(this).closest("tr").find(".view").hide(),e(this).closest("tr").find(".edit").show(),e(this).hide(),e("button.add-line-item").trigger("click"),e("button.cancel-action").attr("data-reload",!0),window.wcTracks.recordEvent("order_edit_edit_item_click",{order_id:woocommerce_admin_meta_boxes.post_id,status:e("#order_status").val()}),!1},delete_item:function(){var o=woocommerce_admin_meta_boxes.remove_item_notice;if(e(this).parents("tbody#order_fee_line_items").length&&(o=woocommerce_admin_meta_boxes.remove_fee_notice),e(this).parents("tbody#order_shipping_line_items").length&&(o=woocommerce_admin_meta_boxes.remove_shipping_notice),window.confirm(o)){var r=e(this).closest("tr.item, tr.fee, tr.shipping").attr("data-order_item_id");t.block();var a=e.extend({},t.get_taxable_address(),{order_id:woocommerce_admin_meta_boxes.post_id,order_item_ids:r,action:"woocommerce_remove_order_item",security:woocommerce_admin_meta_boxes.order_item_nonce});"true"===e("button.cancel-action").attr("data-reload")&&(a.items=e("table.woocommerce_order_items :input[name], .wc-order-totals-items :input[name]").serialize()),a=t.filter_data("delete_item",a),e.ajax({url:woocommerce_admin_meta_boxes.ajax_url,data:a,type:"POST",success:function(o){o.success?(e("#woocommerce-order-items").find(".inside").empty(),e("#woocommerce-order-items").find(".inside").append(o.data.html),o.data.notes_html&&(e("ul.order_notes").empty(),e("ul.order_notes").append(e(o.data.notes_html).find("li"))),t.reloaded_items(),t.unblock()):window.alert(o.data.error),t.unblock()},complete:function(){window.wcTracks.recordEvent("order_edit_remove_item",{order_id:woocommerce_admin_meta_boxes.post_id,status:e("#order_status").val()})}})}return!1},delete_tax:function(){if(window.confirm(woocommerce_admin_meta_boxes.i18n_delete_tax)){t.block();var o={action:"woocommerce_remove_order_tax",rate_id:e(this).attr("data-rate_id"),order_id:woocommerce_admin_meta_boxes.post_id,security:woocommerce_admin_meta_boxes.order_item_nonce};o=t.filter_data("delete_tax",o),e.ajax({url:woocommerce_admin_meta_boxes.ajax_url,data:o,type:"POST",success:function(o){o.success?(e("#woocommerce-order-items").find(".inside").empty(),e("#woocommerce-order-items").find(".inside").append(o.data.html),t.reloaded_items(),t.unblock()):window.alert(o.data.error),t.unblock()},complete:function(){window.wcTracks.recordEvent("order_edit_delete_tax",{order_id:o.order_id,status:e("#order_status").val()})}})}else window.wcTracks.recordEvent("order_edit_delete_tax_cancel",{order_id:woocommerce_admin_meta_boxes.post_id,status:e("#order_status").val()});return!1},get_taxable_address:function(){var o="",t="",r="",a="";return"shipping"===woocommerce_admin_meta_boxes.tax_based_on&&(o=e("#_shipping_country").val(),t=e("#_shipping_state").val(),r=e("#_shipping_postcode").val(),a=e("#_shipping_city").val()),"billing"!==woocommerce_admin_meta_boxes.tax_based_on&&o||(o=e("#_billing_country").val(),t=e("#_billing_state").val(),r=e("#_billing_postcode").val(),a=e("#_billing_city").val()),{country:o,state:t,postcode:r,city:a}},recalculate:function(){if(window.confirm(woocommerce_admin_meta_boxes.calc_totals)){t.block();var o=e.extend({},t.get_taxable_address(),{action:"woocommerce_calc_line_taxes",order_id:woocommerce_admin_meta_boxes.post_id,items:e("table.woocommerce_order_items :input[name], .wc-order-totals-items :input[name]").serialize(),security:woocommerce_admin_meta_boxes.calc_totals_nonce});o=t.filter_data("recalculate",o),e(document.body).trigger("order-totals-recalculate-before",o),e.ajax({url:woocommerce_admin_meta_boxes.ajax_url,data:o,type:"POST",success:function(o){e("#woocommerce-order-items").find(".inside").empty(),e("#woocommerce-order-items").find(".inside").append(o),t.reloaded_items(),t.unblock(),e(document.body).trigger("order-totals-recalculate-success",o)},complete:function(o){e(document.body).trigger("order-totals-recalculate-complete",o),window.wcTracks.recordEvent("order_edit_recalc_totals",{order_id:woocommerce_admin_meta_boxes.post_id,ok_cancel:"OK",status:e("#order_status").val()})}})}else window.wcTracks.recordEvent("order_edit_recalc_totals",{order_id:woocommerce_admin_meta_boxes.post_id,ok_cancel:"cancel",status:e("#order_status").val()});return!1},save_line_items:function(){var o={order_id:woocommerce_admin_meta_boxes.post_id,items:e("table.woocommerce_order_items :input[name], .wc-order-totals-items :input[name]").serialize(),action:"woocommerce_save_order_items",security:woocommerce_admin_meta_boxes.order_item_nonce};return o=t.filter_data("save_line_items",o),t.block(),e.ajax({url:woocommerce_admin_meta_boxes.ajax_url,data:o,type:"POST",success:function(o){o.success?(e("#woocommerce-order-items").find(".inside").empty(),e("#woocommerce-order-items").find(".inside").append(o.data.html),o.data.notes_html&&(e("ul.order_notes").empty(),e("ul.order_notes").append(e(o.data.notes_html).find("li"))),t.reloaded_items(),t.unblock()):(t.unblock(),window.alert(o.data.error))},complete:function(){window.wcTracks.recordEvent("order_edit_save_line_items",{order_id:woocommerce_admin_meta_boxes.post_id,status:e("#order_status").val()})}}),e(this).trigger("items_saved"),!1},refunds:{do_refund:function(){if(t.block(),window.confirm(woocommerce_admin_meta_boxes.i18n_do_refund)){var o=e("input#refund_amount").val(),r=e("input#refund_reason").val(),a=e("input#refunded_amount").val(),i={},n={},d={};e(".refund input.refund_order_item_qty").each(function(o,t){e(t).closest("tr").data("order_item_id")&&t.value&&(i[e(t).closest("tr").data("order_item_id")]=t.value)}),e(".refund input.refund_line_total").each(function(o,t){e(t).closest("tr").data("order_item_id")&&(n[e(t).closest("tr").data("order_item_id")]=accounting.unformat(t.value,woocommerce_admin.mon_decimal_point))}),e(".refund input.refund_line_tax").each(function(o,t){if(e(t).closest("tr").data("order_item_id")){var r=e(t).data("tax_id");d[e(t).closest("tr").data("order_item_id")]||(d[e(t).closest("tr").data("order_item_id")]={}),d[e(t).closest("tr").data("order_item_id")][r]=accounting.unformat(t.value,woocommerce_admin.mon_decimal_point)}});var c={action:"woocommerce_refund_line_items",order_id:woocommerce_admin_meta_boxes.post_id,refund_amount:o,refunded_amount:a,refund_reason:r,line_item_qtys:JSON.stringify(i,null,""),line_item_totals:JSON.stringify(n,null,""),line_item_tax_totals:JSON.stringify(d,null,""),api_refund:e(this).is(".do-api-refund"),restock_refunded_items:e("#restock_refunded_items:checked").length?"true":"false",security:woocommerce_admin_meta_boxes.order_item_nonce};c=t.filter_data("do_refund",c),e.ajax({url:woocommerce_admin_meta_boxes.ajax_url,data:c,type:"POST",success:function(e){!0===e.success?window.location.reload():(window.alert(e.data.error),t.reload_items(),t.unblock())},complete:function(){window.wcTracks.recordEvent("order_edit_refunded",{order_id:c.order_id,status:e("#order_status").val(),api_refund:c.api_refund,has_reason:Boolean(c.refund_reason.length),restock:"true"===c.restock_refunded_items})}})}else t.unblock()},delete_refund:function(){if(window.confirm(woocommerce_admin_meta_boxes.i18n_delete_refund)){var o=e(this).closest("tr.refund").attr("data-order_refund_id");t.block();var r={action:"woocommerce_delete_refund",refund_id:o,security:woocommerce_admin_meta_boxes.order_item_nonce};r=t.filter_data("delete_refund",r),e.ajax({url:woocommerce_admin_meta_boxes.ajax_url,data:r,type:"POST",success:function(){t.reload_items()}})}return!1},input_changed:function(){var o=0,t=e(".woocommerce_order_items").find("tr.item, tr.fee, tr.shipping"),r="yes"===woocommerce_admin_meta_boxes.round_at_subtotal;t.each(function(){e(this).find(".refund input:not(.refund_order_item_qty)").each(function(t,a){var i=accounting.unformat(e(a).val()||0,woocommerce_admin.mon_decimal_point);o+=parseFloat(r?i:accounting.formatNumber(i,woocommerce_admin_meta_boxes.currency_format_num_decimals,""))})}),e("#refund_amount").val(accounting.formatNumber(o,woocommerce_admin_meta_boxes.currency_format_num_decimals,"",woocommerce_admin.mon_decimal_point)).trigger("change")},amount_changed:function(){var o=accounting.unformat(e(this).val(),woocommerce_admin.mon_decimal_point);e("button .wc-order-refund-amount .amount").text(accounting.formatMoney(o,{symbol:woocommerce_admin_meta_boxes.currency_format_symbol,decimal:woocommerce_admin_meta_boxes.currency_format_decimal_sep,thousand:woocommerce_admin_meta_boxes.currency_format_thousand_sep,precision:woocommerce_admin_meta_boxes.currency_format_num_decimals,format:woocommerce_admin_meta_boxes.currency_format}))},refund_quantity_changed:function(){var o=e(this).closest("tr.item"),t=o.find("input.quantity").val(),r=e(this).val(),a=e("input.line_total",o),i=e("input.refund_line_total",o),n=accounting.unformat(a.attr("data-total"),woocommerce_admin.mon_decimal_point)/t;i.val(parseFloat(accounting.formatNumber(n*r,woocommerce_admin_meta_boxes.rounding_precision,"")).toString().replace(".",woocommerce_admin.mon_decimal_point)).trigger("change"),e(".refund_line_tax",o).each(function(){var a=e(this),i=a.data("tax_id"),n=e('input.line_tax[data-tax_id="'+i+'"]',o),d=accounting.unformat(n.data("total_tax"),woocommerce_admin.mon_decimal_point)/t;if(0<d){var c="yes"===woocommerce_admin_meta_boxes.round_at_subtotal,_=woocommerce_admin_meta_boxes[c?"rounding_precision":"currency_format_num_decimals"];a.val(parseFloat(accounting.formatNumber(d*r,_,"")).toString().replace(".",woocommerce_admin.mon_decimal_point)).trigger("change")}else a.val(0).trigger("change")}),r>0?e("#restock_refunded_items").closest("tr").show():(e("#restock_refunded_items").closest("tr").hide(),e(".woocommerce_order_items input.refund_order_item_qty").each(function(){e(this).val()>0&&e("#restock_refunded_items").closest("tr").show()})),e(this).trigger("refund_quantity_changed")}},item_meta:{add:function(){var o=e(this).closest("tr.item, tr.shipping"),t=o.find("tbody.meta_items"),r=t.find("tr").length+1,a='<tr data-meta_id="0"><td><input type="text" maxlength="255" placeholder="'+woocommerce_admin_meta_boxes_order.placeholder_name+'" name="meta_key['+o.attr("data-order_item_id")+"][new-"+r+']" /><textarea placeholder="'+woocommerce_admin_meta_boxes_order.placeholder_value+'" name="meta_value['+o.attr("data-order_item_id")+"][new-"+r+']"></textarea></td><td width="1%"><button class="remove_order_item_meta button">&times;</button></td></tr>';return t.append(a),!1},remove:function(){if(window.confirm(woocommerce_admin_meta_boxes.remove_item_meta)){var o=e(this).closest("tr");o.find(":input").val(""),o.hide()}return!1}},backbone:{init:function(o,t){"wc-modal-add-products"===t&&(e(document.body).trigger("wc-enhanced-select-init"),e(this).on("change",".wc-product-search",function(){if(e(this).closest("tr").is(":last-child")){var o=e(this).closest("table.widefat").find("tbody"),t=o.find("tr").length,r=o.data("row").replace(/\[0\]/g,"["+t+"]");o.append("<tr>"+r+"</tr>"),e(document.body).trigger("wc-enhanced-select-init")}}))},response:function(o,r,a){if("wc-modal-add-tax"===r){var i=a.add_order_tax,n="";a.manual_tax_rate_id&&(n=a.manual_tax_rate_id),t.backbone.add_tax(i,n)}if("wc-modal-add-products"===r){var d=e(this).find("table.widefat").find("tbody").find("tr"),c=[];return e(d).each(function(){var o=e(this).find(':input[name="item_id"]').val(),t=e(this).find(':input[name="item_qty"]').val();c.push({id:o,qty:t||1})}),t.backbone.add_items(c)}},add_items:function(o){t.block();var r={action:"woocommerce_add_order_item",order_id:woocommerce_admin_meta_boxes.post_id,security:woocommerce_admin_meta_boxes.order_item_nonce,data:o};"true"===e("button.cancel-action").attr("data-reload")&&(r.items=e("table.woocommerce_order_items :input[name], .wc-order-totals-items :input[name]").serialize()),r=t.filter_data("add_items",r),e.ajax({type:"POST",url:woocommerce_admin_meta_boxes.ajax_url,data:r,success:function(o){o.success?(e("#woocommerce-order-items").find(".inside").empty(),e("#woocommerce-order-items").find(".inside").append(o.data.html),o.data.notes_html&&(e("ul.order_notes").empty(),e("ul.order_notes").append(e(o.data.notes_html).find("li"))),t.reloaded_items(),t.unblock()):(t.unblock(),window.alert(o.data.error))},complete:function(){window.wcTracks.recordEvent("order_edit_add_products",{order_id:woocommerce_admin_meta_boxes.post_id,status:e("#order_status").val()})},dataType:"json"})},add_tax:function(o,r){if(r&&(o=r),!o)return!1;var a=e(".order-tax-id").map(function(){return e(this).val()}).get();if(-1===e.inArray(o,a)){t.block();var i={action:"woocommerce_add_order_tax",rate_id:o,order_id:woocommerce_admin_meta_boxes.post_id,security:woocommerce_admin_meta_boxes.order_item_nonce};i=t.filter_data("add_tax",i),e.ajax({url:woocommerce_admin_meta_boxes.ajax_url,data:i,dataType:"json",type:"POST",success:function(o){o.success?(e("#woocommerce-order-items").find(".inside").empty(),e("#woocommerce-order-items").find(".inside").append(o.data.html),t.reloaded_items()):window.alert(o.data.error),t.unblock()},complete:function(){window.wcTracks.recordEvent("order_edit_add_tax",{order_id:woocommerce_admin_meta_boxes.post_id,status:e("#order_status").val()})}})}else window.alert(woocommerce_admin_meta_boxes.i18n_tax_rate_already_exists)}},stupidtable:{init:function(){e(".woocommerce_order_items").stupidtable(),e(".woocommerce_order_items").on("aftertablesort",this.add_arrows)},add_arrows:function(o,t){var r=e(this).find("th"),a="asc"===t.direction?"&uarr;":"&darr;",i=t.column;r.find(".wc-arrow").remove(),r.eq(i).append('<span class="wc-arrow">'+a+"</span>")}}},r={init:function(){e("#woocommerce-order-notes").on("click","button.add_note",this.add_order_note).on("click","a.delete_note",this.delete_order_note)},add_order_note:function(){if(e("textarea#add_order_note").val()){e("#woocommerce-order-notes").block({message:null,overlayCSS:{background:"#fff",opacity:.6}});var o={action:"woocommerce_add_order_note",post_id:woocommerce_admin_meta_boxes.post_id,note:e("textarea#add_order_note").val(),note_type:e("select#order_note_type").val(),security:woocommerce_admin_meta_boxes.add_order_note_nonce};return e.post(woocommerce_admin_meta_boxes.ajax_url,o,function(t){e("ul.order_notes .no-items").remove(),e("ul.order_notes").prepend(t),e("#woocommerce-order-notes").unblock(),e("#add_order_note").val(""),window.wcTracks.recordEvent("order_edit_add_order_note",{order_id:woocommerce_admin_meta_boxes.post_id,note_type:o.note_type||"private",status:e("#order_status").val()})}),!1}},delete_order_note:function(){if(window.confirm(woocommerce_admin_meta_boxes.i18n_delete_note)){var o=e(this).closest("li.note");e(o).block({message:null,overlayCSS:{background:"#fff",opacity:.6}});var t={action:"woocommerce_delete_order_note",note_id:e(o).attr("rel"),security:woocommerce_admin_meta_boxes.delete_order_note_nonce};e.post(woocommerce_admin_meta_boxes.ajax_url,t,function(){e(o).remove()})}return!1}},a={init:function(){e(".order_download_permissions").on("click","button.grant_access",this.grant_access).on("click","button.revoke_access",this.revoke_access).on("click","#copy-download-link",this.copy_link).on("aftercopy","#copy-download-link",this.copy_success).on("aftercopyfailure","#copy-download-link",this.copy_fail),e(".order_download_permissions .wc-metabox .handlediv").on("click",function(o){o.stopImmediatePropagation(),e(this).closest("h3").trigger("click")})},grant_access:function(){var o=e("#grant_access_id").val();if(o&&0!==o.length){e(".order_download_permissions").block({message:null,overlayCSS:{background:"#fff",opacity:.6}});var t={action:"woocommerce_grant_access_to_download",product_ids:o,loop:e(".order_download_permissions .wc-metabox").length,order_id:woocommerce_admin_meta_boxes.post_id,security:woocommerce_admin_meta_boxes.grant_access_nonce};return e.post(woocommerce_admin_meta_boxes.ajax_url,t,function(o){o&&-1!==parseInt(o)?e(".order_download_permissions .wc-metaboxes").append(o):window.alert(woocommerce_admin_meta_boxes.i18n_download_permission_fail),e(document.body).trigger("wc-init-datepickers"),e("#grant_access_id").val("").trigger("change"),e(".order_download_permissions").unblock()}),!1}},revoke_access:function(){if(window.confirm(woocommerce_admin_meta_boxes.i18n_permission_revoke)){var o=e(this).parent().parent(),t=e(this).attr("rel").split(",")[0],r=e(this).attr("rel").split(",")[1],a=e(this).data("permission_id");if(t>0){e(o).block({message:null,overlayCSS:{background:"#fff",opacity:.6}});var i={action:"woocommerce_revoke_access_to_download",product_id:t,download_id:r,permission_id:a,order_id:woocommerce_admin_meta_boxes.post_id,security:woocommerce_admin_meta_boxes.revoke_access_nonce};e.post(woocommerce_admin_meta_boxes.ajax_url,i,function(){e(o).fadeOut("300",function(){e(o).remove()})})}else e(o).fadeOut("300",function(){e(o).remove()})}return!1},copy_link:function(o){wcClearClipboard(),wcSetClipboard(e(this).attr("href"),e(this)),o.preventDefault()},copy_success:function(){e(this).tipTip({attribute:"data-tip",activation:"focus",fadeIn:50,fadeOut:50,delay:0}).trigger("focus")},copy_fail:function(){e(this).tipTip({attribute:"data-tip-failed",activation:"focus",fadeIn:50,fadeOut:50,delay:0}).trigger("focus")}},i=function(){e("#order_custom").length&&e("#order_custom #the-list").wpList({addBefore:function(e){return e.data+="&order_id="+woocommerce_admin_meta_boxes.post_id+"&action=woocommerce_order_add_meta",e},addAfter:function(){e("table#list-table").show()},delBefore:function(e){return e.data.order_id=woocommerce_admin_meta_boxes.post_id,e.data.action="woocommerce_order_delete_meta",e}})};o.init(),t.init(),r.init(),a.init(),i()});